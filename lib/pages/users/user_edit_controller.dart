import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:myrunway/core/models/user_model.dart';
import 'package:myrunway/core/services/auth_service.dart';
import 'package:myrunway/core/services/user_management_service.dart';
import 'package:myrunway/core/constants/user_roles.dart';

class UserEditController extends GetxController {
  final UserManagementService _userService = Get.find<UserManagementService>();
  final AuthService _authService = Get.find<AuthService>();

  // Reactive variables
  final RxBool _isUpdating = false.obs;
  final Rx<UserModel?> _user = Rx<UserModel?>(null);

  // Getters
  bool get isUpdating => _isUpdating.value;
  UserModel? get user => _user.value;

  // Permission getters
  bool get canEditUsers => _authService.hasPermission(UserRole.master);

  // Initialize with user data
  void initializeWithUser(UserModel user) {
    _user.value = user;
  }

  // Update user
  Future<bool> updateUser(int userId, UserEditRequest request) async {
    if (!canEditUsers) {
      Get.snackbar(
        'خطأ في الصلاحية',
        'ليس لديك صلاحية لتعديل المستخدمين',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }

    _isUpdating.value = true;

    final response = await _userService.updateUser(userId, request);

    if (response.success && response.data != null) {
      _user.value = response.data!;
      Get.snackbar(
        'نجح',
        'تم تحديث المستخدم بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
      _isUpdating.value = false;
      return true;
    } else {
      Get.snackbar(
        'خطأ',
        response.message ?? 'فشل في تحديث المستخدم',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      _isUpdating.value = false;
      return false;
    }
  }
}
