import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:myrunway/core/services/auth_service.dart';
import 'package:myrunway/core/services/user_management_service.dart';
import 'package:myrunway/core/constants/user_roles.dart';

class UserCreateController extends GetxController {
  final UserManagementService _userService = Get.find<UserManagementService>();
  final AuthService _authService = Get.find<AuthService>();

  // Reactive variables
  final RxBool _isCreating = false.obs;

  // Getters
  bool get isCreating => _isCreating.value;

  // Permission getters
  bool get canCreateUsers => _authService.hasPermission(UserRole.master);

  // Create a new user
  Future<bool> createUser(UserCreateRequest request) async {
    if (!canCreateUsers) {
      Get.snackbar(
        'خطأ في الصلاحية',
        'ليس لديك صلاحية لإنشاء مستخدم جديد',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }

    _isCreating.value = true;

    final response = await _userService.createUser(request);

    if (response.success && response.data != null) {
      Get.snackbar(
        'نجح',
        'تم إنشاء المستخدم بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
      _isCreating.value = false;
      return true;
    } else {
      Get.snackbar(
        'خطأ',
        response.message ?? 'فشل في إنشاء المستخدم',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      _isCreating.value = false;
      return false;
    }
  }
}
