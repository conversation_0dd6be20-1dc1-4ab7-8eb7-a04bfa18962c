import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:myrunway/pages/users/user_edit_controller.dart';
import 'package:myrunway/core/services/user_management_service.dart';
import 'package:myrunway/core/models/user_model.dart';
import 'package:myrunway/core/constants/app_colors.dart';
import 'package:myrunway/core/constants/user_roles.dart';
import 'package:myrunway/utils/form_validators.dart';

class UserEditPage extends StatefulWidget {
  final UserModel user;

  const UserEditPage({super.key, required this.user});

  @override
  State<UserEditPage> createState() => _UserEditPageState();
}

class _UserEditPageState extends State<UserEditPage> {
  final _formKey = GlobalKey<FormState>();
  late final TextEditingController _usernameController;
  late final TextEditingController _passwordController;
  late final TextEditingController _firstNameController;
  late final TextEditingController _lastNameController;
  late final TextEditingController _emailController;
  late final TextEditingController _phoneController;
  late final TextEditingController _commissionRateController;

  final UserEditController _controller = Get.put(UserEditController());

  late UserRole _selectedRole;
  bool _obscurePassword = true;

  @override
  void initState() {
    super.initState();
    _usernameController = TextEditingController(text: widget.user.username);
    _passwordController = TextEditingController();
    _firstNameController = TextEditingController(
      text: widget.user.firstName ?? '',
    );
    _lastNameController = TextEditingController(
      text: widget.user.lastName ?? '',
    );
    _emailController = TextEditingController(text: widget.user.email ?? '');
    _phoneController = TextEditingController(text: widget.user.phone ?? '');
    _commissionRateController = TextEditingController(
      text: widget.user.commissionRate?.toString() ?? '',
    );
    _selectedRole = widget.user.role;
  }

  @override
  void dispose() {
    _usernameController.dispose();
    _passwordController.dispose();
    _firstNameController.dispose();
    _lastNameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _commissionRateController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'تعديل ${widget.user.fullName.isNotEmpty ? widget.user.fullName : widget.user.username}',
        ),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Username
              TextFormField(
                controller: _usernameController,
                decoration: const InputDecoration(
                  labelText: 'اسم المستخدم *',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.person),
                  helperText: 'يجب أن يكون فريداً',
                ),
                validator: FormValidators.required,
                textInputAction: TextInputAction.next,
              ),
              const SizedBox(height: 16),

              // Password (optional for edit)
              TextFormField(
                controller: _passwordController,
                decoration: InputDecoration(
                  labelText: 'كلمة المرور الجديدة',
                  border: const OutlineInputBorder(),
                  prefixIcon: const Icon(Icons.lock),
                  suffixIcon: IconButton(
                    icon: Icon(
                      _obscurePassword
                          ? Icons.visibility
                          : Icons.visibility_off,
                    ),
                    onPressed: () {
                      setState(() {
                        _obscurePassword = !_obscurePassword;
                      });
                    },
                  ),
                  helperText: 'اتركه فارغاً إذا كنت لا تريد تغيير كلمة المرور',
                ),
                obscureText: _obscurePassword,
                validator: (value) {
                  if (value != null && value.isNotEmpty) {
                    return FormValidators.validatePassword(value);
                  }
                  return null;
                },
                textInputAction: TextInputAction.next,
              ),
              const SizedBox(height: 16),

              // First Name
              TextFormField(
                controller: _firstNameController,
                decoration: const InputDecoration(
                  labelText: 'الاسم الأول',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.person_outline),
                ),
                textInputAction: TextInputAction.next,
              ),
              const SizedBox(height: 16),

              // Last Name
              TextFormField(
                controller: _lastNameController,
                decoration: const InputDecoration(
                  labelText: 'اسم العائلة',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.person_outline),
                ),
                textInputAction: TextInputAction.next,
              ),
              const SizedBox(height: 16),

              // Email
              TextFormField(
                controller: _emailController,
                decoration: const InputDecoration(
                  labelText: 'البريد الإلكتروني',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.email),
                ),
                validator: (value) {
                  if (value != null && value.isNotEmpty) {
                    return FormValidators.validateEmail(value);
                  }
                  return null;
                },
                keyboardType: TextInputType.emailAddress,
                textInputAction: TextInputAction.next,
              ),
              const SizedBox(height: 16),

              // Phone
              TextFormField(
                controller: _phoneController,
                decoration: const InputDecoration(
                  labelText: 'رقم الهاتف',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.phone),
                ),
                validator: (value) {
                  if (value != null && value.isNotEmpty) {
                    return FormValidators.phone(value);
                  }
                  return null;
                },
                keyboardType: TextInputType.phone,
                textInputAction: TextInputAction.next,
              ),
              const SizedBox(height: 16),

              // Role Selection (only allow Manager/Employee, not Master)
              DropdownButtonFormField<UserRole>(
                value: _selectedRole,
                decoration: const InputDecoration(
                  labelText: 'الدور *',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.admin_panel_settings),
                ),
                items: [
                  DropdownMenuItem(
                    value: UserRole.manager,
                    child: Text(UserRole.manager.displayName),
                  ),
                  DropdownMenuItem(
                    value: UserRole.employee,
                    child: Text(UserRole.employee.displayName),
                  ),
                  // Show current role even if it's Master (but don't allow changing to Master)
                  if (widget.user.role == UserRole.master)
                    DropdownMenuItem(
                      value: UserRole.master,
                      child: Text(UserRole.master.displayName),
                    ),
                ],
                onChanged: (value) {
                  setState(() {
                    _selectedRole = value!;
                    // Clear commission rate if not employee
                    if (_selectedRole != UserRole.employee) {
                      _commissionRateController.clear();
                    }
                  });
                },
                validator: (value) {
                  if (value == null) {
                    return 'يرجى اختيار الدور';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Commission Rate (only for employees)
              if (_selectedRole == UserRole.employee) ...[
                TextFormField(
                  controller: _commissionRateController,
                  decoration: const InputDecoration(
                    labelText: 'نسبة العمولة',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.percent),
                    suffixText: '%',
                    helperText: 'اختياري - نسبة العمولة للموظف',
                  ),
                  validator: (value) {
                    if (value != null && value.isNotEmpty) {
                      final rate = double.tryParse(value);
                      if (rate == null) {
                        return 'يرجى إدخال رقم صحيح';
                      }
                      if (rate < 0 || rate > 100) {
                        return 'النسبة يجب أن تكون بين 0 و 100';
                      }
                    }
                    return null;
                  },
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(
                      RegExp(r'^\d+\.?\d{0,2}'),
                    ),
                  ],
                  textInputAction: TextInputAction.done,
                ),
                const SizedBox(height: 16),
              ],

              const SizedBox(height: 16),

              // Update Button
              Obx(
                () => ElevatedButton(
                  onPressed: _controller.isUpdating ? null : _updateUser,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child:
                      _controller.isUpdating
                          ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Colors.white,
                              ),
                            ),
                          )
                          : const Text(
                            'تحديث المستخدم',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _updateUser() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final request = UserEditRequest(
      username:
          _usernameController.text.trim() != widget.user.username
              ? _usernameController.text.trim()
              : null,
      password:
          _passwordController.text.trim().isNotEmpty
              ? _passwordController.text.trim()
              : null,
      firstName:
          _firstNameController.text.trim() != (widget.user.firstName ?? '')
              ? _firstNameController.text.trim().isEmpty
                  ? null
                  : _firstNameController.text.trim()
              : null,
      lastName:
          _lastNameController.text.trim() != (widget.user.lastName ?? '')
              ? _lastNameController.text.trim().isEmpty
                  ? null
                  : _lastNameController.text.trim()
              : null,
      email:
          _emailController.text.trim() != (widget.user.email ?? '')
              ? _emailController.text.trim().isEmpty
                  ? null
                  : _emailController.text.trim()
              : null,
      phone:
          _phoneController.text.trim() != (widget.user.phone ?? '')
              ? _phoneController.text.trim().isEmpty
                  ? null
                  : _phoneController.text.trim()
              : null,
      role: _selectedRole != widget.user.role ? _selectedRole : null,
      commissionRate:
          _commissionRateController.text.trim() !=
                  (widget.user.commissionRate?.toString() ?? '')
              ? _commissionRateController.text.trim().isEmpty
                  ? null
                  : double.parse(_commissionRateController.text.trim())
              : null,
    );

    if (request.isEmpty) {
      Get.snackbar(
        'تنبيه',
        'لم يتم تغيير أي بيانات',
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
      return;
    }

    final success = await _controller.updateUser(widget.user.id!, request);
    if (success) {
      Get.back(closeOverlays: true);
    }
  }
}
