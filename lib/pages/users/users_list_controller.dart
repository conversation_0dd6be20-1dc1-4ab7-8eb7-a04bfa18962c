import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:myrunway/core/models/user_model.dart';
import 'package:myrunway/core/services/auth_service.dart';
import 'package:myrunway/core/services/user_management_service.dart';
import 'package:myrunway/core/constants/user_roles.dart';

class UsersListController extends GetxController {
  final UserManagementService _userService = Get.find<UserManagementService>();
  final AuthService _authService = Get.find<AuthService>();

  // Reactive variables
  final RxList<UserModel> _users = <UserModel>[].obs;
  final RxBool _isLoading = false.obs;
  final RxBool _isDeleting = false.obs;
  final Rx<UserModel?> _selectedUser = Rx<UserModel?>(null);

  // Getters
  List<UserModel> get users => _users;
  bool get isLoading => _isLoading.value;
  bool get isDeleting => _isDeleting.value;
  UserModel? get selectedUser => _selectedUser.value;

  // Permission getters
  bool get canViewUsers => _authService.hasPermission(UserRole.manager);
  bool get canCreateUsers => _authService.hasPermission(UserRole.master);
  bool get canEditUsers => _authService.hasPermission(UserRole.master);
  bool get canDeleteUsers => _authService.hasPermission(UserRole.master);

  @override
  void onInit() {
    super.onInit();
    loadUsers();
  }

  // Load all users
  Future<void> loadUsers() async {
    if (!canViewUsers) {
      Get.snackbar(
        'خطأ في الصلاحية',
        'ليس لديك صلاحية لعرض المستخدمين',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    _isLoading.value = true;

    final response = await _userService.getOfficeUsers();

    if (response.success && response.data != null) {
      _users.value = response.data!;
    } else {
      Get.snackbar(
        'خطأ',
        response.message ?? 'فشل في جلب قائمة المستخدمين',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }

    _isLoading.value = false;
  }

  // Delete a user
  Future<bool> deleteUser(int userId) async {
    if (!canDeleteUsers) {
      Get.snackbar(
        'خطأ في الصلاحية',
        'ليس لديك صلاحية لحذف المستخدمين',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }

    _isDeleting.value = true;

    final response = await _userService.deleteUser(userId);

    if (response.success) {
      _users.removeWhere((user) => user.id == userId);
      Get.snackbar(
        'نجح',
        'تم حذف المستخدم بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
      _isDeleting.value = false;
      return true;
    } else {
      Get.snackbar(
        'خطأ',
        response.message ?? 'فشل في حذف المستخدم',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      _isDeleting.value = false;
      return false;
    }
  }

  // Select a user
  void selectUser(UserModel user) {
    _selectedUser.value = user;
  }

  // Clear selection
  void clearSelection() {
    _selectedUser.value = null;
  }

  // Refresh users list
  Future<void> refreshUsers() async {
    await loadUsers();
  }
}
