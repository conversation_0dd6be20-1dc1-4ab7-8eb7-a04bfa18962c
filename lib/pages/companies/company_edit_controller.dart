import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:myrunway/core/models/company_model.dart';
import 'package:myrunway/core/services/auth_service.dart';
import 'package:myrunway/core/services/company_service.dart';
import 'package:myrunway/core/constants/user_roles.dart';

class CompanyEditController extends GetxController {
  final CompanyService _companyService = Get.find<CompanyService>();
  final AuthService _authService = Get.find<AuthService>();

  // Reactive variables
  final RxBool _isUpdating = false.obs;
  final Rx<CompanyModel?> _company = Rx<CompanyModel?>(null);

  // Getters
  bool get isUpdating => _isUpdating.value;
  CompanyModel? get company => _company.value;

  // Permission getters
  bool get canEditCompanies => _authService.hasPermission(UserRole.manager);

  // Initialize with company data
  void initializeWithCompany(CompanyModel company) {
    _company.value = company;
  }

  // Update company
  Future<bool> updateCompany(int companyId, CompanyEditRequest request) async {
    if (!canEditCompanies) {
      Get.snackbar(
        'خطأ في الصلاحية',
        'ليس لديك صلاحية لتعديل الشركات',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }

    _isUpdating.value = true;

    final response = await _companyService.updateCompany(companyId, request);

    if (response.success && response.data != null) {
      _company.value = response.data!;
      Get.snackbar(
        'نجح',
        'تم تحديث الشركة بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
      _isUpdating.value = false;
      return true;
    } else {
      Get.snackbar(
        'خطأ',
        response.message ?? 'فشل في تحديث الشركة',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      _isUpdating.value = false;
      return false;
    }
  }
}
