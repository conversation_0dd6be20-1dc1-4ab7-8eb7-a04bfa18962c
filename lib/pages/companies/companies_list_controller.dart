import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:myrunway/core/models/company_model.dart';
import 'package:myrunway/core/services/auth_service.dart';
import 'package:myrunway/core/services/company_service.dart';
import 'package:myrunway/core/constants/user_roles.dart';

class CompaniesListController extends GetxController {
  final CompanyService _companyService = Get.find<CompanyService>();
  final AuthService _authService = Get.find<AuthService>();

  // Reactive variables
  final RxList<CompanyModel> _companies = <CompanyModel>[].obs;
  final RxBool _isLoading = false.obs;
  final Rx<CompanyModel?> _selectedCompany = Rx<CompanyModel?>(null);

  // Getters
  List<CompanyModel> get companies => _companies;
  bool get isLoading => _isLoading.value;
  CompanyModel? get selectedCompany => _selectedCompany.value;

  // Permission getters
  bool get canViewCompanies => _authService.hasPermission(UserRole.manager);
  bool get canCreateCompanies => _authService.hasPermission(UserRole.manager);
  bool get canEditCompanies => _authService.hasPermission(UserRole.manager);
  bool get canDeleteCompanies => _authService.hasPermission(UserRole.master);

  @override
  void onInit() {
    super.onInit();
    loadCompanies();
  }

  // Load all companies
  Future<void> loadCompanies() async {
    if (!canViewCompanies) {
      Get.snackbar(
        'خطأ في الصلاحية',
        'ليس لديك صلاحية لعرض الشركات',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    _isLoading.value = true;

    final response = await _companyService.getCompanies();

    if (response.success && response.data != null) {
      _companies.value = response.data!;
    } else {
      Get.snackbar(
        'خطأ',
        response.message ?? 'فشل في جلب قائمة الشركات',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }

    _isLoading.value = false;
  }

  // Delete a company
  Future<bool> deleteCompany(int companyId) async {
    if (!canDeleteCompanies) {
      Get.snackbar(
        'خطأ في الصلاحية',
        'ليس لديك صلاحية لحذف الشركات',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }

    final response = await _companyService.deleteCompany(companyId);

    if (response.success) {
      _companies.removeWhere((company) => company.id == companyId);
      Get.snackbar(
        'نجح',
        'تم حذف الشركة بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
      return true;
    } else {
      Get.snackbar(
        'خطأ',
        response.message ?? 'فشل في حذف الشركة',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }
  }

  // Select a company
  void selectCompany(CompanyModel company) {
    _selectedCompany.value = company;
  }

  // Clear selection
  void clearSelection() {
    _selectedCompany.value = null;
  }

  // Refresh companies list
  Future<void> refreshCompanies() async {
    await loadCompanies();
  }
}
