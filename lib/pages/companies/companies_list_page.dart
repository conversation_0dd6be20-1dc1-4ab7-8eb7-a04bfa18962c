import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:myrunway/pages/companies/companies_list_controller.dart';
import 'package:myrunway/core/models/company_model.dart';
import 'package:myrunway/core/constants/app_colors.dart';
import 'package:myrunway/pages/companies/company_create_page.dart';
import 'package:myrunway/pages/companies/company_edit_page.dart';

class CompaniesListPage extends StatelessWidget {
  const CompaniesListPage({super.key});

  @override
  Widget build(BuildContext context) {
    final CompaniesListController controller = Get.put(
      CompaniesListController(),
    );

    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة الشركات'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          if (controller.canCreateCompanies)
            IconButton(
              icon: const Icon(Icons.add),
              onPressed: () => Get.to(() => const CompanyCreatePage()),
            ),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (controller.companies.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.business, size: 64, color: Colors.grey[400]),
                const SizedBox(height: 16),
                Text(
                  'لا توجد شركات',
                  style: TextStyle(fontSize: 18, color: Colors.grey[600]),
                ),
                const SizedBox(height: 16),
                if (controller.canCreateCompanies)
                  ElevatedButton.icon(
                    onPressed: () => Get.to(() => const CompanyCreatePage()),
                    icon: const Icon(Icons.add),
                    label: const Text('إضافة شركة جديدة'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                    ),
                  ),
              ],
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: controller.refreshCompanies,
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: controller.companies.length,
            itemBuilder: (context, index) {
              final company = controller.companies[index];
              return _CompanyCard(company: company, controller: controller);
            },
          ),
        );
      }),
    );
  }
}

class _CompanyCard extends StatelessWidget {
  final CompanyModel company;
  final CompaniesListController controller;

  const _CompanyCard({required this.company, required this.controller});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () => controller.selectCompany(company),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  // Company color indicator
                  if (company.colorCode != null)
                    Container(
                      width: 20,
                      height: 20,
                      decoration: BoxDecoration(
                        color: Color(
                          int.parse(
                            company.colorCode!.replaceFirst('#', '0xFF'),
                          ),
                        ),
                        shape: BoxShape.circle,
                      ),
                    ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      company.name,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  // Status indicator
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: company.isActive ? Colors.green : Colors.red,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      company.isActive ? 'نشط' : 'غير نشط',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
              if (company.address != null) ...[
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(Icons.location_on, size: 16, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        company.address!,
                        style: TextStyle(color: Colors.grey[600], fontSize: 14),
                      ),
                    ),
                  ],
                ),
              ],
              if (company.phone != null) ...[
                const SizedBox(height: 4),
                Row(
                  children: [
                    Icon(Icons.phone, size: 16, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Text(
                      company.phone!,
                      style: TextStyle(color: Colors.grey[600], fontSize: 14),
                    ),
                  ],
                ),
              ],
              const SizedBox(height: 12),
              Row(
                children: [
                  Text(
                    'المكتب: ${company.office.name}',
                    style: TextStyle(color: Colors.grey[600], fontSize: 12),
                  ),
                  const Spacer(),
                  if (controller.canEditCompanies)
                    IconButton(
                      icon: const Icon(Icons.edit, size: 20),
                      onPressed:
                          () => Get.to(() => CompanyEditPage(company: company)),
                      color: AppColors.primary,
                    ),
                  if (controller.canDeleteCompanies)
                    IconButton(
                      icon: const Icon(Icons.delete, size: 20),
                      onPressed: () => _showDeleteDialog(context),
                      color: Colors.red,
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showDeleteDialog(BuildContext context) {
    Get.dialog(
      AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف شركة "${company.name}"؟'),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('إلغاء')),
          TextButton(
            onPressed: () {
              Get.back();
              if (company.id != null) {
                controller.deleteCompany(company.id!);
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }
}
