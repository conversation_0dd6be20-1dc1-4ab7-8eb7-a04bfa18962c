import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:myrunway/core/models/company_model.dart';
import 'package:myrunway/core/services/auth_service.dart';
import 'package:myrunway/core/services/company_service.dart';
import 'package:myrunway/core/constants/user_roles.dart';

class CompanyCreateController extends GetxController {
  final CompanyService _companyService = Get.find<CompanyService>();
  final AuthService _authService = Get.find<AuthService>();

  // Reactive variables
  final RxBool _isCreating = false.obs;

  // Getters
  bool get isCreating => _isCreating.value;

  // Permission getters
  bool get canCreateCompanies => _authService.hasPermission(UserRole.manager);

  // Create a new company
  Future<bool> createCompany(CompanyCreateRequest request) async {
    if (!canCreateCompanies) {
      Get.snackbar(
        'خطأ في الصلاحية',
        'ليس لديك صلاحية لإنشاء شركة جديدة',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }

    _isCreating.value = true;

    final response = await _companyService.createCompany(request);

    if (response.success && response.data != null) {
      Get.snackbar(
        'نجح',
        'تم إنشاء الشركة بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
      _isCreating.value = false;
      return true;
    } else {
      Get.snackbar(
        'خطأ',
        response.message ?? 'فشل في إنشاء الشركة',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      _isCreating.value = false;
      return false;
    }
  }
}
