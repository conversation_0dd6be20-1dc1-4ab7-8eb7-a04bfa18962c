import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:myrunway/core/models/order_model.dart';

class OrdersController extends GetxController {
  // Observable variables
  final RxList<Order> allOrders = <Order>[].obs;
  final RxList<Order> filteredOrders = <Order>[].obs;

  final Rx<OrderStatus?> selectedStatus = Rx<OrderStatus?>(null);
  final Rx<HandlingStatus?> selectedHandlingStatus = Rx<HandlingStatus?>(null);
  final RxString selectedAssignee = ''.obs;
  final Rx<DateTimeRange?> selectedDateRange = Rx<DateTimeRange?>(null);

  @override
  void onInit() {
    super.onInit();
    _initializeMockData();
    filteredOrders.value = allOrders;
  }

  void refreshOrders() {
    _initializeMockData();
    filteredOrders.value = allOrders;
  }

  void onOrderTap(int index) {
    // Handle order tap
  }

  void applyFilters() {
    filteredOrders.value =
        allOrders.where((order) {
          bool matchesStatus =
              selectedStatus.value == null ||
              order.status == selectedStatus.value;
          bool matchesHandlingStatus =
              selectedHandlingStatus.value == null ||
              order.handlingStatus == selectedHandlingStatus.value;
          bool matchesAssignee =
              selectedAssignee.value.isEmpty ||
              order.assignee == selectedAssignee.value;
          bool matchesDate =
              selectedDateRange.value == null ||
              (order.assignedDate.isAfter(
                    selectedDateRange.value!.start.subtract(Duration(days: 1)),
                  ) &&
                  order.assignedDate.isBefore(
                    selectedDateRange.value!.end.add(Duration(days: 1)),
                  ));

          return matchesStatus &&
              matchesHandlingStatus &&
              matchesAssignee &&
              matchesDate;
        }).toList();
  }

  void clearFilters() {
    selectedStatus.value = null;
    selectedHandlingStatus.value = null;
    selectedAssignee.value = '';
    selectedDateRange.value = null;
    filteredOrders.value = allOrders;
  }

  // Helper methods for statistics
  int get deliveredOrdersCount =>
      allOrders.where((order) => order.status == OrderStatus.delivered).length;

  int get totalOrdersCount => allOrders.length;

  double get totalMoneyToCollect => allOrders
      .where((order) => order.status == OrderStatus.delivered)
      .fold(0.0, (sum, order) => sum + order.totalAmount);

  double get totalMoneyCollected => allOrders
      .where((order) => order.status == OrderStatus.delivered)
      .fold(0.0, (sum, order) => sum + order.collectedAmount);

  double get totalCommissionFromDelivered => allOrders
      .where((order) => order.status == OrderStatus.delivered)
      .fold(0.0, (sum, order) => sum + order.commissionAmount);

  void _initializeMockData() {
    allOrders.value = [
      Order(
        id: 'ORD-001',
        customerName: 'أحمد محمد علي',
        customerPhone: '+966501234567',
        totalAmount: 1250.50,
        assignedDate: DateTime.now().subtract(Duration(days: 1)),
        assignee: 'سارة أحمد',
        status: OrderStatus.pending,
        handlingStatus: HandlingStatus.assigned,
        address: 'الرياض، حي النخيل، شارع الملك فهد',
        itemsCount: 3,
        collectedAmount: 0.0, // Not collected yet (pending)
        commissionRate: 0.05, // 5% commission
        commissionAmount: 0.0, // No commission until delivered
      ),
      Order(
        id: 'ORD-002',
        customerName: 'محمد علي أحمد',
        customerPhone: '+966501234568',
        totalAmount: 1500.75,
        assignedDate: DateTime.now().subtract(Duration(days: 2)),
        assignee: 'محمد علي',
        status: OrderStatus.delivered,
        handlingStatus: HandlingStatus.completed,
        address: 'الرياض، حي النخيل، شارع الملك فهد',
        itemsCount: 2,
        collectedAmount: 1500.75, // Fully collected
        commissionRate: 0.05, // 5% commission
        commissionAmount: 75.04, // 5% of 1500.75
      ),
      Order(
        id: 'ORD-003',
        customerName: 'محمد علي أحمد',
        customerPhone: '+966501234568',
        totalAmount: 1500.75,
        assignedDate: DateTime.now().subtract(Duration(days: 2)),
        assignee: 'محمد علي',
        status: OrderStatus.delivered,
        handlingStatus: HandlingStatus.completed,
        address: 'الرياض، حي النخيل، شارع الملك فهد',
        itemsCount: 2,
        collectedAmount: 1200.00, // Partially collected
        commissionRate: 0.05, // 5% commission
        commissionAmount: 60.00, // 5% of collected amount
      ),
      Order(
        id: 'ORD-004',
        customerName: 'محمد علي أحمد',
        customerPhone: '+966501234568',
        totalAmount: 1500.75,
        assignedDate: DateTime.now().subtract(Duration(days: 2)),
        assignee: 'محمد علي',
        status: OrderStatus.shipped,
        handlingStatus: HandlingStatus.inProgress,
        address: 'الرياض، حي النخيل، شارع الملك فهد',
        itemsCount: 2,
        collectedAmount: 0.0, // Not delivered yet
        commissionRate: 0.05, // 5% commission
        commissionAmount: 0.0, // No commission until delivered
      ),
    ];
  }
}
