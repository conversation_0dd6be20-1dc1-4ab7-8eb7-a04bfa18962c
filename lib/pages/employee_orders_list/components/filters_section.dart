import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:myrunway/pages/employee_orders_list/employees_orders_list.dart';
import 'package:myrunway/core/models/order_model.dart';

class FiltersSection extends StatelessWidget {
  const FiltersSection({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<OrdersController>();

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 20),
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Color(0xFF667eea).withOpacity(0.1),
            blurRadius: 20,
            offset: Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(8),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Color(0xFF667eea), Color(0xFF764ba2)],
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(Icons.tune_rounded, color: Colors.white, size: 20),
              ),
              SizedBox(width: 12),
              Text(
                'تصفية وبحث متقدم',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF2C3E50),
                ),
              ),
              Spacer(),
              Material(
                color: Color(0xFFE74C3C).withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                child: InkWell(
                  borderRadius: BorderRadius.circular(12),
                  onTap: controller.clearFilters,
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    child: Text(
                      'مسح الكل',
                      style: TextStyle(
                        color: Color(0xFFE74C3C),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 16),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              _buildFilterChip(
                'حالة الطلب',
                controller.selectedStatus.value?.toString(),
                () => _showStatusFilter(context, controller),
              ),
              _buildFilterChip(
                'حالة المعالجة',
                controller.selectedHandlingStatus.value?.toString(),
                () => _showHandlingStatusFilter(context, controller),
              ),
              _buildFilterChip(
                'المسؤول',
                controller.selectedAssignee.value.isEmpty
                    ? null
                    : controller.selectedAssignee.value,
                () => _showAssigneeFilter(context, controller),
              ),
              _buildFilterChip(
                'التاريخ',
                controller.selectedDateRange.value != null ? 'محدد' : null,
                () => _showDateFilter(context, controller),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, String? value, VoidCallback onTap) {
    bool isActive = value != null;
    return Material(
      color: isActive ? Color(0xFF667eea).withOpacity(0.1) : Color(0xFFF8F9FA),
      borderRadius: BorderRadius.circular(16),
      child: InkWell(
        borderRadius: BorderRadius.circular(16),
        onTap: onTap,
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 10),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: isActive ? Color(0xFF667eea) : Color(0xFFE0E6ED),
              width: 1.5,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                label,
                style: TextStyle(
                  color: isActive ? Color(0xFF667eea) : Color(0xFF7F8C8D),
                  fontWeight: isActive ? FontWeight.w600 : FontWeight.w500,
                  fontSize: 14,
                ),
              ),
              if (isActive) ...[
                SizedBox(width: 6),
                Container(
                  padding: EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    color: Color(0xFF667eea),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(Icons.check, size: 12, color: Colors.white),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  void _showStatusFilter(BuildContext context, OrdersController controller) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder:
          (context) => Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.vertical(top: Radius.circular(25)),
            ),
            padding: EdgeInsets.all(25),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Color(0xFFE0E6ED),
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                SizedBox(height: 20),
                Text(
                  'اختر حالة الطلب',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF2C3E50),
                  ),
                ),
                SizedBox(height: 20),
                ...OrderStatus.values.map(
                  (status) => _buildStatusOption(status, controller, context),
                ),
              ],
            ),
          ),
    );
  }

  Widget _buildStatusOption(
    OrderStatus status,
    OrdersController controller,
    BuildContext context,
  ) {
    return Container(
      margin: EdgeInsets.only(bottom: 8),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () {
            controller.selectedStatus.value = status;
            controller.applyFilters();
            Navigator.pop(context);
          },
          child: Container(
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Color(0xFFF8F9FA),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                Container(
                  width: 12,
                  height: 12,
                  decoration: BoxDecoration(
                    color: _getStatusColor(status),
                    shape: BoxShape.circle,
                  ),
                ),
                SizedBox(width: 12),
                Text(
                  _getStatusText(status),
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF2C3E50),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showHandlingStatusFilter(
    BuildContext context,
    OrdersController controller,
  ) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder:
          (context) => Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.vertical(top: Radius.circular(25)),
            ),
            padding: EdgeInsets.all(25),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Color(0xFFE0E6ED),
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                SizedBox(height: 20),
                Text(
                  'اختر حالة المعالجة',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF2C3E50),
                  ),
                ),
                SizedBox(height: 20),
                ...HandlingStatus.values.map(
                  (status) =>
                      _buildHandlingStatusOption(status, controller, context),
                ),
              ],
            ),
          ),
    );
  }

  Widget _buildHandlingStatusOption(
    HandlingStatus status,
    OrdersController controller,
    BuildContext context,
  ) {
    return Container(
      margin: EdgeInsets.only(bottom: 8),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () {
            controller.selectedHandlingStatus.value = status;
            controller.applyFilters();
            Navigator.pop(context);
          },
          child: Container(
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Color(0xFFF8F9FA),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                Container(
                  width: 12,
                  height: 12,
                  decoration: BoxDecoration(
                    color: _getHandlingStatusColor(status),
                    shape: BoxShape.circle,
                  ),
                ),
                SizedBox(width: 12),
                Text(
                  _getHandlingStatusText(status),
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF2C3E50),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showAssigneeFilter(BuildContext context, OrdersController controller) {
    List<String> assignees =
        controller.allOrders.map((o) => o.assignee).toSet().toList();
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder:
          (context) => Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.vertical(top: Radius.circular(25)),
            ),
            padding: EdgeInsets.all(25),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Color(0xFFE0E6ED),
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                SizedBox(height: 20),
                Text(
                  'اختر المسؤول',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF2C3E50),
                  ),
                ),
                SizedBox(height: 20),
                ...assignees.map(
                  (assignee) => Container(
                    margin: EdgeInsets.only(bottom: 8),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(12),
                        onTap: () {
                          controller.selectedAssignee.value = assignee;
                          controller.applyFilters();
                          Navigator.pop(context);
                        },
                        child: Container(
                          padding: EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Color(0xFFF8F9FA),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Row(
                            children: [
                              Container(
                                padding: EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: Color(0xFF667eea).withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Icon(
                                  Icons.person_rounded,
                                  color: Color(0xFF667eea),
                                  size: 20,
                                ),
                              ),
                              SizedBox(width: 12),
                              Text(
                                assignee,
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                  color: Color(0xFF2C3E50),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
    );
  }

  void _showDateFilter(
    BuildContext context,
    OrdersController controller,
  ) async {
    DateTimeRange? range = await showDateRangePicker(
      context: context,
      firstDate: DateTime.now().subtract(Duration(days: 365)),
      lastDate: DateTime.now().add(Duration(days: 30)),
      initialDateRange: controller.selectedDateRange.value,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: Color(0xFF667eea),
              onPrimary: Colors.white,
              onSurface: Color(0xFF2C3E50),
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(foregroundColor: Color(0xFF667eea)),
            ),
          ),
          child: child!,
        );
      },
    );
    if (range != null) {
      controller.selectedDateRange.value = range;
      controller.applyFilters();
    }
  }

  Color _getStatusColor(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return Color(0xFFFF6B35);
      case OrderStatus.processing:
        return Color(0xFF4A90E2);
      case OrderStatus.shipped:
        return Color(0xFF9B59B6);
      case OrderStatus.delivered:
        return Color(0xFF27AE60);
      case OrderStatus.cancelled:
        return Color(0xFFE74C3C);
    }
  }

  Color _getHandlingStatusColor(HandlingStatus status) {
    switch (status) {
      case HandlingStatus.assigned:
        return Color(0xFF1ABC9C);
      case HandlingStatus.inProgress:
        return Color(0xFFF39C12);
      case HandlingStatus.completed:
        return Color(0xFF2ECC71);
      case HandlingStatus.onHold:
        return Color(0xFF95A5A6);
    }
  }

  String _getStatusText(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return 'في الانتظار';
      case OrderStatus.processing:
        return 'قيد المعالجة';
      case OrderStatus.shipped:
        return 'تم الشحن';
      case OrderStatus.delivered:
        return 'تم التسليم';
      case OrderStatus.cancelled:
        return 'ملغي';
    }
  }

  String _getHandlingStatusText(HandlingStatus status) {
    switch (status) {
      case HandlingStatus.assigned:
        return 'تم التعيين';
      case HandlingStatus.inProgress:
        return 'قيد التنفيذ';
      case HandlingStatus.completed:
        return 'مكتمل';
      case HandlingStatus.onHold:
        return 'معلق';
    }
  }
}
