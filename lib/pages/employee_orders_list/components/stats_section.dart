import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:myrunway/pages/employee_orders_list/employees_orders_list.dart';
import 'package:myrunway/core/models/order_model.dart';

class StatsSection extends StatelessWidget {
  const StatsSection({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<OrdersController>();

    return Container(
      margin: EdgeInsets.all(20),
      child: Column(
        children: [
          // First row - existing stats
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'إجمالي الطلبات',
                  '${controller.totalOrdersCount}',
                  Icons.inventory_2_rounded,
                  Color(0xFF667eea),
                ),
              ),
              SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'بحاجة للشحن',
                  '${controller.allOrders.where((o) => o.status == OrderStatus.pending || o.status == OrderStatus.processing).length}',
                  Icons.local_shipping_rounded,
                  Color(0xFFFF6B35),
                ),
              ),
              SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'تم التسليم',
                  '${controller.deliveredOrdersCount}',
                  Icons.check_circle_rounded,
                  Color(0xFF27AE60),
                ),
              ),
            ],
          ),
          SizedBox(height: 16),
          // Second row - new stats
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'معدل التسليم',
                  '${controller.totalOrdersCount > 0 ? ((controller.deliveredOrdersCount / controller.totalOrdersCount) * 100).toStringAsFixed(1) : "0"}%',
                  Icons.trending_up_rounded,
                  Color(0xFF9B59B6),
                ),
              ),
              SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'المبلغ المحصل',
                  '${controller.totalMoneyCollected.toStringAsFixed(0)} جنيه',
                  Icons.account_balance_wallet_rounded,
                  Color(0xFF1ABC9C),
                ),
              ),
              SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'إجمالي العمولة',
                  '${controller.totalCommissionFromDelivered.toStringAsFixed(0)} جنيه',
                  Icons.monetization_on_rounded,
                  Color(0xFFF39C12),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.15),
            blurRadius: 15,
            offset: Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(icon, color: color, size: 28),
          ),
          SizedBox(height: 12),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2C3E50),
            ),
          ),
          SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Color(0xFF7F8C8D),
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
