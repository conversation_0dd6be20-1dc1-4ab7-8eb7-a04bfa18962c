import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:myrunway/pages/employee_orders_list/employees_orders_list_page.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  int _currentIndex = 0;

  final List<Widget> _pages = [
    const OrdersPage(),
    const Center(child: Text('المنتجات', style: TextStyle(fontSize: 24))),
    const Center(child: Text('العملاء', style: TextStyle(fontSize: 24))),
    const Center(child: Text('التقارير', style: TextStyle(fontSize: 24))),
    const Center(child: Text('الإعدادات', style: TextStyle(fontSize: 24))),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: IndexedStack(index: _currentIndex, children: _pages),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 20,
              offset: const Offset(0, -5),
            ),
          ],
        ),
        child: SafeArea(
          child: Container(
            height: 80,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildNavItem(
                  0,
                  Icons.shopping_bag_outlined,
                  Icons.shopping_bag,
                  'الطلبات',
                ),
                _buildNavItem(
                  1,
                  Icons.inventory_2_outlined,
                  Icons.inventory_2,
                  'المنتجات',
                ),
                _buildNavItem(2, Icons.people_outline, Icons.people, 'العملاء'),
                _buildNavItem(
                  3,
                  Icons.analytics_outlined,
                  Icons.analytics,
                  'التقارير',
                ),
                _buildNavItem(
                  4,
                  Icons.settings_outlined,
                  Icons.settings,
                  'الإعدادات',
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNavItem(
    int index,
    IconData icon,
    IconData activeIcon,
    String label,
  ) {
    final isActive = _currentIndex == index;

    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        setState(() {
          _currentIndex = index;
        });
      },
      behavior: HitTestBehavior.opaque,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color:
              isActive
                  ? const Color(0xFF8B5CF6).withOpacity(0.1)
                  : Colors.transparent,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            AnimatedSwitcher(
              duration: const Duration(milliseconds: 200),
              child: Icon(
                isActive ? activeIcon : icon,
                key: ValueKey(isActive),
                color: isActive ? const Color(0xFF8B5CF6) : Colors.grey[400],
                size: 24,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                fontWeight: isActive ? FontWeight.w600 : FontWeight.w400,
                color: isActive ? const Color(0xFF8B5CF6) : Colors.grey[400],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
