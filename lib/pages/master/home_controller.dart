import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:myrunway/core/services/auth_service.dart';
import 'package:myrunway/pages/companies/companies_list_page.dart';
import 'package:myrunway/pages/orders/orders_list_page.dart';
import 'package:myrunway/pages/users/users_list_page.dart';

class MasterHomeController extends GetxController {
  final AuthService authService = Get.find<AuthService>();

  final RxBool isLoading = false.obs;
  final RxList<Map<String, dynamic>> recentActivities =
      <Map<String, dynamic>>[].obs;
  final RxMap<String, dynamic> systemStats = <String, dynamic>{}.obs;
  final RxList<Map<String, dynamic>> officePerformance =
      <Map<String, dynamic>>[].obs;

  @override
  void onInit() {
    super.onInit();
    loadDashboardData();
  }

  Future<void> loadDashboardData() async {
    isLoading.value = true;

    // Simulate API calls
    await Future.delayed(const Duration(seconds: 1));

    // Load system statistics
    systemStats.value = {
      'totalOrders': 1250,
      'totalRevenue': 125000.50,
      'totalOffices': 1,
      'activeOffices': 4,
      'totalEmployees': 0,
      'activeEmployees': 38,
      'totalClients': 180,
      'activeClients': 165,
    };

    // Load office performance data
    officePerformance.value = [
      {
        'id': 'office_1',
        'name': 'مكتب الرياض الرئيسي',
        'performance': 85,
        'orders': 450,
        'revenue': 45000.0,
        'employees': 15,
      },
      {
        'id': 'office_2',
        'name': 'مكتب جدة',
        'performance': 78,
        'orders': 320,
        'revenue': 32000.0,
        'employees': 12,
      },
      {
        'id': 'office_3',
        'name': 'مكتب الدمام',
        'performance': 92,
        'orders': 280,
        'revenue': 28000.0,
        'employees': 10,
      },
      {
        'id': 'office_4',
        'name': 'مكتب المدينة',
        'performance': 67,
        'orders': 200,
        'revenue': 20000.0,
        'employees': 8,
      },
    ];

    // Load recent activities
    recentActivities.value = [
      {
        'id': '1',
        'type': 'office_created',
        'title': 'تم إنشاء مكتب جديد في الطائف',
        'subtitle': 'بواسطة النظام',
        'timestamp': DateTime.now().subtract(const Duration(minutes: 30)),
        'icon': Icons.business,
        'color': Colors.green,
      },
      {
        'id': '2',
        'type': 'manager_added',
        'title': 'تم إضافة مدير جديد',
        'subtitle': 'خالد محمد - مكتب جدة',
        'timestamp': DateTime.now().subtract(const Duration(hours: 1)),
        'icon': Icons.person_add,
        'color': Colors.blue,
      },
      {
        'id': '3',
        'type': 'report_generated',
        'title': 'تم إنشاء تقرير شهري',
        'subtitle': 'تقرير الأداء العام',
        'timestamp': DateTime.now().subtract(const Duration(hours: 2)),
        'icon': Icons.analytics,
        'color': Colors.orange,
      },
      {
        'id': '4',
        'type': 'system_update',
        'title': 'تحديث النظام',
        'subtitle': 'تم تحديث النظام إلى الإصدار 2.1.0',
        'timestamp': DateTime.now().subtract(const Duration(hours: 6)),
        'icon': Icons.system_update,
        'color': Colors.purple,
      },
    ];

    isLoading.value = false;
  }

  Future<void> refreshDashboard() async {
    await loadDashboardData();
    Get.snackbar(
      'تم التحديث',
      'تم تحديث بيانات لوحة التحكم',
      backgroundColor: Colors.green,
      colorText: Colors.white,
    );
  }

  // Navigation methods
  void navigateToOffices() {
    Get.snackbar(
      'قريباً',
      'سيتم تطوير صفحة إدارة المكاتب قريباً',
      backgroundColor: Colors.blue,
      colorText: Colors.white,
    );
    // TODO: Navigate to offices management page
    // Get.to(() => MasterOfficesListPage());
  }

  void navigateToEmployees() {
    Get.to(() => const UsersListPage());
  }

  void navigateToOrders() {
    Get.to(() => const OrdersListPage());
  }

  void navigateToClients() {
    Get.to(() => const CompaniesListPage());
  }

  void navigateToReports() {
    Get.snackbar(
      'قريباً',
      'سيتم تطوير صفحة التقارير المتقدمة قريباً',
      backgroundColor: Colors.blue,
      colorText: Colors.white,
    );
    // TODO: Navigate to advanced reports page
    // Get.to(() => MasterReportsPage());
  }

  void navigateToSystemSettings() {
    Get.snackbar(
      'قريباً',
      'سيتم تطوير صفحة إعدادات النظام قريباً',
      backgroundColor: Colors.blue,
      colorText: Colors.white,
    );
    // TODO: Navigate to system settings page
    // Get.to(() => MasterSystemSettingsPage());
  }

  // Data getters
  int get totalOrders => systemStats['totalOrders'] ?? 0;
  double get totalRevenue => systemStats['totalRevenue'] ?? 0.0;
  int get totalOffices => systemStats['totalOffices'] ?? 0;
  int get activeOffices => systemStats['activeOffices'] ?? 0;
  int get totalEmployees => systemStats['totalEmployees'] ?? 0;
  int get activeEmployees => systemStats['activeEmployees'] ?? 0;
  int get totalClients => systemStats['totalClients'] ?? 0;
  int get activeClients => systemStats['activeClients'] ?? 0;

  String get formattedTotalRevenue {
    return '${totalRevenue.toStringAsFixed(2).replaceAllMapped(RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (Match m) => '${m[1]},')} ر.س';
  }

  double get systemEfficiency {
    if (totalOrders == 0) return 0.0;
    // Calculate based on completed orders vs total orders
    // This is mock data - in real app, this would come from API
    return 87.5; // 87.5% efficiency
  }

  double get revenueGrowth {
    // Mock data - in real app, this would be calculated from historical data
    return 12.3; // 12.3% growth
  }

  double get employeeUtilization {
    if (totalEmployees == 0) return 0.0;
    return (activeEmployees / totalEmployees) * 100;
  }

  double get officeUtilization {
    if (totalOffices == 0) return 0.0;
    return (activeOffices / totalOffices) * 100;
  }

  // Office performance helpers
  Map<String, dynamic>? getTopPerformingOffice() {
    if (officePerformance.isEmpty) return null;

    return officePerformance.reduce((current, next) {
      return current['performance'] > next['performance'] ? current : next;
    });
  }

  Map<String, dynamic>? getLowestPerformingOffice() {
    if (officePerformance.isEmpty) return null;

    return officePerformance.reduce((current, next) {
      return current['performance'] < next['performance'] ? current : next;
    });
  }

  double get averageOfficePerformance {
    if (officePerformance.isEmpty) return 0.0;

    final total = officePerformance.fold<double>(
      0.0,
      (sum, office) => sum + office['performance'],
    );

    return total / officePerformance.length;
  }

  // Activity helpers
  String formatActivityTime(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else {
      return 'منذ ${difference.inDays} يوم';
    }
  }

  Color getActivityColor(String type) {
    switch (type) {
      case 'office_created':
        return Colors.green;
      case 'manager_added':
        return Colors.blue;
      case 'report_generated':
        return Colors.orange;
      case 'system_update':
        return Colors.purple;
      case 'employee_added':
        return Colors.teal;
      case 'client_added':
        return Colors.indigo;
      default:
        return Colors.grey;
    }
  }

  IconData getActivityIcon(String type) {
    switch (type) {
      case 'office_created':
        return Icons.business;
      case 'manager_added':
        return Icons.person_add;
      case 'report_generated':
        return Icons.analytics;
      case 'system_update':
        return Icons.system_update;
      case 'employee_added':
        return Icons.group_add;
      case 'client_added':
        return Icons.business_center;
      default:
        return Icons.info;
    }
  }

  // Quick actions
  void createNewOffice() {
    Get.snackbar(
      'قريباً',
      'سيتم تطوير إنشاء مكتب جديد قريباً',
      backgroundColor: Colors.blue,
      colorText: Colors.white,
    );
  }

  void generateSystemReport() {
    Get.snackbar(
      'قريباً',
      'سيتم تطوير إنشاء تقرير النظام قريباً',
      backgroundColor: Colors.blue,
      colorText: Colors.white,
    );
  }

  void manageSystemUsers() {
    Get.snackbar(
      'قريباً',
      'سيتم تطوير إدارة مستخدمي النظام قريباً',
      backgroundColor: Colors.blue,
      colorText: Colors.white,
    );
  }

  void viewSystemLogs() {
    Get.snackbar(
      'قريباً',
      'سيتم تطوير عرض سجلات النظام قريباً',
      backgroundColor: Colors.blue,
      colorText: Colors.white,
    );
  }
}
