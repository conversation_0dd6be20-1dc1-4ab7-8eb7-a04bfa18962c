import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:myrunway/pages/master/home_controller.dart';
import 'package:myrunway/core/constants/app_colors.dart';
import 'package:myrunway/widgets/drawers/main_drawer.dart';

class MasterHomePage extends StatelessWidget {
  const MasterHomePage({super.key});

  @override
  Widget build(BuildContext context) {
    final MasterHomeController controller = Get.put(MasterHomeController());

    return Scaffold(
      backgroundColor: AppColors.background,
      drawer: const MainDrawer(),
      appBar: AppBar(
        title: const Text('لوحة تحكم المدير العام'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications_outlined),
            onPressed: () {
              // Show notifications
            },
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: controller.refreshDashboard,
          ),
        ],
      ),
      body: Obx(
        () =>
            controller.isLoading.value
                ? const Center(child: CircularProgressIndicator())
                : SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Welcome Section
                      _buildWelcomeSection(controller),

                      // const SizedBox(height: 24),

                      // // System Overview
                      // _buildSystemOverview(controller),
                      const SizedBox(height: 24),

                      // Quick Actions
                      _buildQuickActions(controller),

                      // const SizedBox(height: 24),

                      // // Office Performance
                      // _buildOfficePerformance(controller),
                      // const SizedBox(height: 24),

                      // // Recent System Activities
                      // _buildRecentActivities(controller),
                    ],
                  ),
                ),
      ),
    );
  }

  Widget _buildWelcomeSection(MasterHomeController controller) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [AppColors.primary, AppColors.primaryLight],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withOpacity(0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.admin_panel_settings,
                color: Colors.white,
                size: 32,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'مرحباً، ${controller.authService.currentUser?.firstName ?? 'المدير'}',
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const Text(
                      'المدير العام للنظام',
                      style: TextStyle(fontSize: 16, color: Colors.white70),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              const Icon(
                Icons.business_center,
                color: Colors.white70,
                size: 16,
              ),
              const SizedBox(width: 8),
              Text(
                'إدارة ${controller.totalOffices} مكاتب',
                style: const TextStyle(fontSize: 14, color: Colors.white70),
              ),
              const SizedBox(width: 16),
              const Icon(Icons.people, color: Colors.white70, size: 16),
              const SizedBox(width: 8),
              Text(
                '${controller.totalEmployees} موظف',
                style: const TextStyle(fontSize: 14, color: Colors.white70),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSystemOverview(MasterHomeController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'نظرة عامة على النظام',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 1.2,
          children: [
            _buildOverviewCard(
              'إجمالي الطلبات',
              '${controller.totalOrders}',
              Icons.shopping_bag,
              AppColors.primary,
              '+12% من الأمس',
            ),
            _buildOverviewCard(
              'إجمالي الإيرادات',
              controller.formattedTotalRevenue,
              Icons.monetization_on,
              AppColors.success,
              '+8% من الأمس',
            ),
            _buildOverviewCard(
              'المكاتب النشطة',
              '${controller.activeOffices}',
              Icons.business,
              AppColors.info,
              '${controller.totalOffices} إجمالي',
            ),
            _buildOverviewCard(
              'الموظفين النشطين',
              '${controller.activeEmployees}',
              Icons.people,
              AppColors.warning,
              '${controller.totalEmployees} إجمالي',
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildOverviewCard(
    String title,
    String value,
    IconData icon,
    Color color,
    String subtitle,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 20),
              ),
              Icon(Icons.trending_up, color: AppColors.success, size: 16),
            ],
          ),
          const Spacer(),
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            subtitle,
            style: TextStyle(fontSize: 10, color: AppColors.textSecondary),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions(MasterHomeController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'إجراءات سريعة',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 3,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 1.0,
          children: [
            // _buildActionCard(
            //   'إدارة المكاتب',
            //   Icons.business,
            //   AppColors.primary,
            //   () => controller.navigateToOffices(),
            // ),
            _buildActionCard(
              'إدارة الموظفين',
              Icons.people,
              AppColors.success,
              () => controller.navigateToEmployees(),
            ),
            _buildActionCard(
              'إدارة الطلبات',
              Icons.assignment,
              AppColors.info,
              () => controller.navigateToOrders(),
            ),
            _buildActionCard(
              'إدارة العملاء',
              Icons.business_center,
              AppColors.warning,
              () => controller.navigateToClients(),
            ),
            // _buildActionCard(
            //   'التقارير المتقدمة',
            //   Icons.analytics,
            //   AppColors.error,
            //   () => controller.navigateToReports(),
            // ),
            // _buildActionCard(
            //   'إعدادات النظام',
            //   Icons.settings,
            //   AppColors.grey600,
            //   () => controller.navigateToSystemSettings(),
            // ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard(
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: color, size: 20),
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: const TextStyle(fontSize: 11, fontWeight: FontWeight.w600),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOfficePerformance(MasterHomeController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'أداء المكاتب',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              _buildOfficePerformanceItem(
                'مكتب الرياض الرئيسي',
                85,
                AppColors.success,
              ),
              const SizedBox(height: 12),
              _buildOfficePerformanceItem('مكتب جدة', 78, AppColors.info),
              const SizedBox(height: 12),
              _buildOfficePerformanceItem('مكتب الدمام', 92, AppColors.primary),
              const SizedBox(height: 12),
              _buildOfficePerformanceItem(
                'مكتب المدينة',
                67,
                AppColors.warning,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildOfficePerformanceItem(
    String officeName,
    int performance,
    Color color,
  ) {
    return Row(
      children: [
        Expanded(
          flex: 3,
          child: Text(
            officeName,
            style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
          ),
        ),
        Expanded(
          flex: 2,
          child: LinearProgressIndicator(
            value: performance / 100,
            backgroundColor: color.withOpacity(0.2),
            valueColor: AlwaysStoppedAnimation<Color>(color),
          ),
        ),
        const SizedBox(width: 8),
        Text(
          '$performance%',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  Widget _buildRecentActivities(MasterHomeController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الأنشطة الأخيرة',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: const Column(
            children: [
              ListTile(
                leading: CircleAvatar(
                  backgroundColor: AppColors.success,
                  child: Icon(Icons.business, color: Colors.white, size: 16),
                ),
                title: Text('تم إنشاء مكتب جديد في الطائف'),
                subtitle: Text('بواسطة النظام - منذ 30 دقيقة'),
                contentPadding: EdgeInsets.zero,
              ),
              Divider(),
              ListTile(
                leading: CircleAvatar(
                  backgroundColor: AppColors.info,
                  child: Icon(Icons.person_add, color: Colors.white, size: 16),
                ),
                title: Text('تم إضافة مدير جديد'),
                subtitle: Text('خالد محمد - مكتب جدة - منذ ساعة'),
                contentPadding: EdgeInsets.zero,
              ),
              Divider(),
              ListTile(
                leading: CircleAvatar(
                  backgroundColor: AppColors.warning,
                  child: Icon(Icons.analytics, color: Colors.white, size: 16),
                ),
                title: Text('تم إنشاء تقرير شهري'),
                subtitle: Text('تقرير الأداء العام - منذ 2 ساعة'),
                contentPadding: EdgeInsets.zero,
              ),
            ],
          ),
        ),
      ],
    );
  }
}
