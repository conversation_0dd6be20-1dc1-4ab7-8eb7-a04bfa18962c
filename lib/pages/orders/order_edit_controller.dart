import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:myrunway/core/models/order_model_new.dart';
import 'package:myrunway/core/models/company_model.dart';
import 'package:myrunway/core/services/auth_service.dart';
import 'package:myrunway/core/services/order_service.dart';
import 'package:myrunway/core/services/company_service.dart';
import 'package:myrunway/core/constants/user_roles.dart';

class OrderEditController extends GetxController {
  final OrderService _orderService = Get.find<OrderService>();
  final CompanyService _companyService = Get.find<CompanyService>();
  final AuthService _authService = Get.find<AuthService>();

  // Reactive variables
  final RxBool _isUpdating = false.obs;
  final Rx<OrderModelNew?> _order = Rx<OrderModelNew?>(null);
  final RxList<CompanyModel> _companies = <CompanyModel>[].obs;
  final RxBool _isLoadingCompanies = false.obs;

  // Getters
  bool get isUpdating => _isUpdating.value;
  OrderModelNew? get order => _order.value;
  List<CompanyModel> get companies => _companies;
  bool get isLoadingCompanies => _isLoadingCompanies.value;

  // Permission getters
  bool get canEditOrders => _authService.hasPermission(UserRole.manager);

  @override
  void onInit() {
    super.onInit();
    loadCompanies();
  }

  // Initialize with order data
  void initializeWithOrder(OrderModelNew order) {
    _order.value = order;
  }

  // Load companies for dropdown
  Future<void> loadCompanies() async {
    _isLoadingCompanies.value = true;

    final response = await _companyService.getCompanies();

    if (response.success && response.data != null) {
      _companies.value = response.data!;
    } else {
      Get.snackbar(
        'خطأ',
        response.message ?? 'فشل في جلب قائمة الشركات',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }

    _isLoadingCompanies.value = false;
  }

  // Update order
  Future<bool> updateOrder(int orderId, OrderCreateRequest request) async {
    if (!canEditOrders) {
      Get.snackbar(
        'خطأ في الصلاحية',
        'ليس لديك صلاحية لتعديل الطلبات',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }

    _isUpdating.value = true;

    final response = await _orderService.updateOrder(orderId, request);

    if (response.success && response.data != null) {
      _order.value = response.data!;
      Get.snackbar(
        'نجح',
        'تم تحديث الطلب بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
      _isUpdating.value = false;
      return true;
    } else {
      Get.snackbar(
        'خطأ',
        response.message ?? 'فشل في تحديث الطلب',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      _isUpdating.value = false;
      return false;
    }
  }
}
