import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:myrunway/pages/orders/order_details_controller.dart';
import 'package:myrunway/core/constants/app_colors.dart';
import 'package:myrunway/core/models/order_model_new.dart';
import 'package:url_launcher/url_launcher.dart';

class OrderDetailsPage extends StatelessWidget {
  final int orderId;

  const OrderDetailsPage({super.key, required this.orderId});

  @override
  Widget build(BuildContext context) {
    final OrderDetailsController controller = Get.put(
      OrderDetailsController(orderId: orderId),
    );

    return Scaffold(
      appBar: AppBar(
        title: const Text('تفاصيل الطلب'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: controller.refreshOrderDetails,
          ),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (controller.hasError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error_outline, size: 64, color: Colors.red[400]),
                const SizedBox(height: 16),
                Text(
                  controller.error,
                  style: TextStyle(fontSize: 16, color: Colors.red[600]),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton.icon(
                  onPressed: controller.refreshOrderDetails,
                  icon: const Icon(Icons.refresh),
                  label: const Text('إعادة المحاولة'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          );
        }

        if (controller.orderWithHistory == null) {
          return const Center(child: Text('لا توجد بيانات للطلب'));
        }

        return RefreshIndicator(
          onRefresh: controller.refreshOrderDetails,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Order Basic Information
                _OrderInfoCard(controller: controller),
                const SizedBox(height: 16),

                // Customer Information
                _CustomerInfoCard(controller: controller),
                const SizedBox(height: 16),

                // Pricing Information
                _PricingInfoCard(controller: controller),
                const SizedBox(height: 16),

                // Assignment Information
                if (controller.orderWithHistory!.order.assignedTo != null)
                  _AssignmentInfoCard(controller: controller),
                if (controller.orderWithHistory!.order.assignedTo != null)
                  const SizedBox(height: 16),

                // Action Buttons
                _ActionButtonsCard(controller: controller),
                const SizedBox(height: 16),

                // Order Timeline/History
                _OrderTimelineCard(controller: controller),
              ],
            ),
          ),
        );
      }),
    );
  }
}

class _OrderInfoCard extends StatelessWidget {
  final OrderDetailsController controller;

  const _OrderInfoCard({required this.controller});

  @override
  Widget build(BuildContext context) {
    final order = controller.orderWithHistory!.order;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.receipt_long, color: AppColors.primary),
                const SizedBox(width: 8),
                const Text(
                  'معلومات الطلب',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            _InfoRow(
              icon: Icons.tag,
              label: 'رقم الداخلي',
              value: '#${order.id}',
            ),
            _InfoRow(icon: Icons.tag, label: 'رقم الطلب', value: order.code),
            _InfoRow(
              icon: Icons.info,
              label: 'الحالة',
              value: order.orderHandlingStatus.displayName,
              valueColor: order.orderHandlingStatus.color,
            ),
            if (order.orderDeliveryStatus != null)
              _InfoRow(
                icon: Icons.local_shipping,
                label: 'حالة التسليم',
                value: order.orderDeliveryStatus!.name,
              ),
            _InfoRow(
              icon: Icons.calendar_today,
              label: 'تاريخ الإنشاء',
              value: _formatDateTime(order.createdAt),
            ),
            if (order.deliveryDeadlineDate != null)
              _InfoRow(
                icon: Icons.schedule,
                label: 'موعد التسليم',
                value: _formatDateTime(order.deliveryDeadlineDate!),
                valueColor: order.isOverdue ? Colors.red : null,
              ),
            if (order.notes != null && order.notes!.isNotEmpty)
              _InfoRow(icon: Icons.note, label: 'ملاحظات', value: order.notes!),
            _InfoRow(
              icon: order.breakable ? Icons.warning : Icons.check_circle,
              label: 'قابل للكسر',
              value: order.breakable ? 'نعم' : 'لا',
              valueColor: order.breakable ? Colors.orange : Colors.green,
            ),
          ],
        ),
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}

class _CustomerInfoCard extends StatelessWidget {
  final OrderDetailsController controller;

  const _CustomerInfoCard({required this.controller});

  @override
  Widget build(BuildContext context) {
    final order = controller.orderWithHistory!.order;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.person, color: AppColors.primary),
                const SizedBox(width: 8),
                const Text(
                  'معلومات العميل',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            _InfoRow(
              icon: Icons.person,
              label: 'اسم العميل',
              value: order.customerName,
            ),
            InkWell(
              onTap: () async {
                final uri = Uri.parse('tel:${order.customerPhone}');
                if (await canLaunchUrl(uri)) {
                  await launchUrl(uri);
                }
              },
              child: _InfoRow(
                icon: Icons.phone,
                label: 'رقم الهاتف',
                value: '${order.customerPhone} (للاتصال)',
                valueColor: Colors.blue,
              ),
            ),
            if (order.customerAddress != null &&
                order.customerAddress!.isNotEmpty)
              _InfoRow(
                icon: Icons.location_on,
                label: 'العنوان',
                value: order.customerAddress!,
              ),
            if (order.customerCompany != null)
              _InfoRow(
                icon: Icons.business,
                label: 'الشركة',
                value: order.customerCompany!.name,
              ),
          ],
        ),
      ),
    );
  }
}

class _PricingInfoCard extends StatelessWidget {
  final OrderDetailsController controller;

  const _PricingInfoCard({required this.controller});

  @override
  Widget build(BuildContext context) {
    final order = controller.orderWithHistory!.order;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.attach_money, color: AppColors.primary),
                const SizedBox(width: 8),
                const Text(
                  'معلومات التسعير',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            if (order.totalPrice != null)
              _InfoRow(
                icon: Icons.monetization_on,
                label: 'إجمالي السعر',
                value: '${order.totalPrice!.toStringAsFixed(2)} جنيه',
                valueColor: Colors.green[600],
              ),
            if (order.specialCommissionRate != null)
              _InfoRow(
                icon: Icons.payment,
                label: 'العمولة',
                value:
                    '${order.specialCommissionRate!.toStringAsFixed(2)} جنيه',
                valueColor: Colors.blue[600],
              ),
            if (order.deliveryCustomerPayment != null)
              _InfoRow(
                icon: Icons.payment,
                label: 'المبلغ المدفوع',
                value:
                    '${order.deliveryCustomerPayment!.toStringAsFixed(2)} جنيه',
                valueColor: Colors.blue[600],
              ),
          ],
        ),
      ),
    );
  }
}

class _AssignmentInfoCard extends StatelessWidget {
  final OrderDetailsController controller;

  const _AssignmentInfoCard({required this.controller});

  @override
  Widget build(BuildContext context) {
    final order = controller.orderWithHistory!.order;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.person_pin, color: AppColors.primary),
                const SizedBox(width: 8),
                const Text(
                  'معلومات التعيين',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            _InfoRow(
              icon: Icons.person,
              label: 'مُعيَّن إلى',
              value: order.assignedTo!.fullName,
              valueColor: Colors.blue[600],
            ),
            if (order.assignedAt != null)
              _InfoRow(
                icon: Icons.schedule,
                label: 'تاريخ التعيين',
                value: _formatDateTime(order.assignedAt!),
              ),
          ],
        ),
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}

class _ActionButtonsCard extends StatelessWidget {
  final OrderDetailsController controller;

  const _ActionButtonsCard({required this.controller});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.settings, color: AppColors.primary),
                const SizedBox(width: 8),
                const Text(
                  'إجراءات الطلب',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                if (controller.canTransferOrder)
                  _ActionButton(
                    icon: Icons.swap_horiz,
                    label: 'نقل الطلب',
                    onPressed: controller.transferOrder,
                    color: Colors.orange,
                  ),
                if (controller.canChangeStatus)
                  _ActionButton(
                    icon: Icons.update,
                    label: 'تغيير الحالة',
                    onPressed: controller.changeOrderStatus,
                    color: Colors.purple,
                  ),
                if (controller.canEditOrder)
                  _ActionButton(
                    icon: Icons.edit,
                    label: 'تعديل الطلب',
                    onPressed: controller.editOrder,
                    color: Colors.blue,
                  ),
                if (!controller.orderWithHistory!.order.hasAssignee)
                  _ActionButton(
                    icon: Icons.person_add,
                    label: 'تعيين موظف',
                    onPressed: controller.assignOrder,
                    color: Colors.green,
                  ),
                if (controller.canDeleteOrder)
                  _ActionButton(
                    icon: Icons.delete,
                    label: 'حذف الطلب',
                    onPressed: controller.deleteOrder,
                    color: Colors.red,
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class _ActionButton extends StatelessWidget {
  final IconData icon;
  final String label;
  final VoidCallback onPressed;
  final Color color;

  const _ActionButton({
    required this.icon,
    required this.label,
    required this.onPressed,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 18),
      label: Text(label),
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }
}

class _OrderTimelineCard extends StatelessWidget {
  final OrderDetailsController controller;

  const _OrderTimelineCard({required this.controller});

  @override
  Widget build(BuildContext context) {
    final events = controller.getTimelineEvents();

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.timeline, color: AppColors.primary),
                const SizedBox(width: 8),
                const Text(
                  'تاريخ الطلب',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            if (events.isEmpty)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(32),
                  child: Text(
                    'لا توجد أحداث في تاريخ هذا الطلب',
                    style: TextStyle(color: Colors.grey),
                  ),
                ),
              )
            else
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: events.length,
                separatorBuilder:
                    (context, index) => const SizedBox(height: 16),
                itemBuilder: (context, index) {
                  final event = events[index];
                  final isLast = index == events.length - 1;

                  return _TimelineItem(event: event, isLast: isLast);
                },
              ),
          ],
        ),
      ),
    );
  }
}

class _TimelineItem extends StatelessWidget {
  final TimelineEvent event;
  final bool isLast;

  const _TimelineItem({required this.event, required this.isLast});

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Timeline indicator
        Column(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: event.type.color,
                shape: BoxShape.circle,
              ),
              child: Icon(event.icon, color: Colors.white, size: 20),
            ),
            if (!isLast)
              Container(
                width: 2,
                height: 40,
                color: Colors.grey[300],
                margin: const EdgeInsets.only(top: 8),
              ),
          ],
        ),
        const SizedBox(width: 16),

        // Event content
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                event.title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                event.description,
                style: TextStyle(fontSize: 14, color: Colors.grey[700]),
              ),
              if (event.note != null && event.note!.isNotEmpty) ...[
                const SizedBox(height: 4),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    'ملاحظة: ${event.note!}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ),
              ],
              const SizedBox(height: 4),
              Row(
                children: [
                  Icon(Icons.access_time, size: 14, color: Colors.grey[500]),
                  const SizedBox(width: 4),
                  Text(
                    _formatDateTime(event.timestamp),
                    style: TextStyle(fontSize: 12, color: Colors.grey[500]),
                  ),
                  if (event.hasImage) ...[
                    const SizedBox(width: 12),
                    Icon(Icons.image, size: 14, color: Colors.blue[500]),
                    const SizedBox(width: 4),
                    Text(
                      'صورة',
                      style: TextStyle(fontSize: 12, color: Colors.blue[500]),
                    ),
                  ],
                  if (event.hasLocation) ...[
                    const SizedBox(width: 12),
                    Icon(Icons.location_on, size: 14, color: Colors.green[500]),
                    const SizedBox(width: 4),
                    Text(
                      'موقع',
                      style: TextStyle(fontSize: 12, color: Colors.green[500]),
                    ),
                  ],
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}

class _InfoRow extends StatelessWidget {
  final IconData icon;
  final String label;
  final String value;
  final Color? valueColor;

  const _InfoRow({
    required this.icon,
    required this.label,
    required this.value,
    this.valueColor,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, size: 16, color: Colors.grey[600]),
          const SizedBox(width: 8),
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: TextStyle(
                color: Colors.grey[700],
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            flex: 4,
            child: Text(
              value,
              style: TextStyle(
                color: valueColor ?? Colors.grey[800],
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
