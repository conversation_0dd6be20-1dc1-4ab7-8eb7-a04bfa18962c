import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:myrunway/core/models/user_model.dart';
import 'package:myrunway/core/models/order_model_new.dart';
import 'package:myrunway/core/services/user_service.dart';
import 'package:myrunway/core/constants/app_colors.dart';
import 'package:myrunway/core/constants/user_roles.dart';

class TransferOrderDialog extends StatefulWidget {
  final OrderModelNew order;
  final Function(OrderTransferRequest) onTransfer;

  const TransferOrderDialog({
    super.key,
    required this.order,
    required this.onTransfer,
  });

  @override
  State<TransferOrderDialog> createState() => _TransferOrderDialogState();
}

class _TransferOrderDialogState extends State<TransferOrderDialog> {
  final UserService _userService = Get.find<UserService>();

  List<UserModel> _employees = [];
  UserModel? _selectedEmployee;
  bool _isLoading = true;
  String _error = '';

  @override
  void initState() {
    super.initState();
    _loadEmployees();
  }

  Future<void> _loadEmployees() async {
    setState(() {
      _isLoading = true;
      _error = '';
    });

    final response = await _userService.getOfficeUsers();

    if (response.success && response.data != null) {
      setState(() {
        // Filter out the currently assigned employee and non-employee users
        _employees =
            response.data!
                .where(
                  (user) =>
                      user.id != widget.order.assignedTo?.id &&
                      user.role == UserRole.employee,
                )
                .toList();
        _isLoading = false;
      });
    } else {
      setState(() {
        _error = response.message ?? 'فشل في جلب قائمة الموظفين';
        _isLoading = false;
      });
    }
  }

  void _handleTransfer() {
    if (_selectedEmployee == null) {
      Get.snackbar(
        'خطأ',
        'يرجى اختيار موظف لنقل الطلب إليه',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    final request = OrderTransferRequest(
      toEmployeeId: _selectedEmployee!.id!,
      proofType: 'PROOF_OF_ASSIGNMENT',
    );

    widget.onTransfer(request);
    Get.back();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          Icon(Icons.swap_horiz, color: AppColors.primary),
          const SizedBox(width: 8),
          const Text('نقل الطلب'),
        ],
      ),
      content: SizedBox(
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'نقل الطلب #${widget.order.code}',
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            if (widget.order.assignedTo != null)
              Text(
                'من: ${widget.order.assignedTo!.fullName}',
                style: TextStyle(fontSize: 14, color: Colors.grey[600]),
              ),
            const SizedBox(height: 16),

            if (_isLoading)
              const Center(child: CircularProgressIndicator())
            else if (_error.isNotEmpty)
              Column(
                children: [
                  Text(_error, style: const TextStyle(color: Colors.red)),
                  const SizedBox(height: 8),
                  ElevatedButton.icon(
                    onPressed: _loadEmployees,
                    icon: const Icon(Icons.refresh),
                    label: const Text('إعادة المحاولة'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              )
            else if (_employees.isEmpty)
              const Text(
                'لا يوجد موظفين متاحين لنقل الطلب إليهم',
                style: TextStyle(color: Colors.grey),
              )
            else
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'اختر الموظف المراد نقل الطلب إليه:',
                    style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(horizontal: 12),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey[300]!),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: DropdownButtonHideUnderline(
                      child: DropdownButton<UserModel>(
                        value: _selectedEmployee,
                        hint: const Text('اختر موظف'),
                        isExpanded: true,
                        items:
                            _employees.map((employee) {
                              return DropdownMenuItem<UserModel>(
                                value: employee,
                                child: Row(
                                  children: [
                                    CircleAvatar(
                                      radius: 16,
                                      backgroundColor: AppColors.primary,
                                      child: Text(
                                        employee.fullName[0].toUpperCase(),
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontSize: 12,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: 12),
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Text(
                                            employee.fullName,
                                            style: const TextStyle(
                                              fontSize: 14,
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                          Text(
                                            employee.role.displayName,
                                            style: TextStyle(
                                              fontSize: 12,
                                              color: Colors.grey[600],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            }).toList(),
                        onChanged: (UserModel? value) {
                          setState(() {
                            _selectedEmployee = value;
                          });
                        },
                      ),
                    ),
                  ),
                ],
              ),
          ],
        ),
      ),
      actions: [
        TextButton(onPressed: () => Get.back(), child: const Text('إلغاء')),
        ElevatedButton(
          onPressed:
              _employees.isNotEmpty && !_isLoading ? _handleTransfer : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primary,
            foregroundColor: Colors.white,
          ),
          child: const Text('نقل الطلب'),
        ),
      ],
    );
  }
}
