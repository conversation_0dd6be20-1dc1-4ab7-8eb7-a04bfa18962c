import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:myrunway/core/models/order_model_new.dart';
import 'package:myrunway/core/constants/app_colors.dart';

class ChangeStatusDialog extends StatefulWidget {
  final OrderModelNew order;
  final Function(OrderStatusUpdateRequest) onStatusChange;

  const ChangeStatusDialog({
    super.key,
    required this.order,
    required this.onStatusChange,
  });

  @override
  State<ChangeStatusDialog> createState() => _ChangeStatusDialogState();
}

class _ChangeStatusDialogState extends State<ChangeStatusDialog> {
  OrderHandlingStatus? _selectedStatus;
  final TextEditingController _noteController = TextEditingController();

  // Available status options based on current status
  List<OrderHandlingStatus> get _availableStatuses {
    final current = widget.order.orderHandlingStatus;
    switch (current) {
      case OrderHandlingStatus.pending:
        return [OrderHandlingStatus.assigned, OrderHandlingStatus.processing];
      case OrderHandlingStatus.assigned:
        return [
          OrderHandlingStatus.pending,
          OrderHandlingStatus.processing,
          OrderHandlingStatus.completed,
        ];
      case OrderHandlingStatus.processing:
        return [OrderHandlingStatus.assigned, OrderHandlingStatus.completed];
      case OrderHandlingStatus.completed:
        return [
          OrderHandlingStatus.processing,
        ]; // Allow reverting from completed
    }
  }

  void _handleStatusChange() {
    if (_selectedStatus == null) {
      Get.snackbar(
        'خطأ',
        'يرجى اختيار الحالة الجديدة',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    final request = OrderStatusUpdateRequest(
      handlingStatus: _selectedStatus!,
      note:
          _noteController.text.trim().isEmpty
              ? null
              : _noteController.text.trim(),
    );

    widget.onStatusChange(request);
    Get.back();
  }

  @override
  void dispose() {
    _noteController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          Icon(Icons.update, color: AppColors.primary),
          const SizedBox(width: 8),
          const Text('تغيير حالة الطلب'),
        ],
      ),
      content: SizedBox(
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تغيير حالة الطلب #${widget.order.code}',
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: widget.order.orderHandlingStatus.color.withValues(
                  alpha: 0.1,
                ),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: widget.order.orderHandlingStatus.color.withValues(
                    alpha: 0.3,
                  ),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: widget.order.orderHandlingStatus.color,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'الحالة الحالية: ${widget.order.orderHandlingStatus.displayName}',
                    style: TextStyle(
                      fontSize: 14,
                      color: widget.order.orderHandlingStatus.color,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),

            const Text(
              'اختر الحالة الجديدة:',
              style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 8),

            if (_availableStatuses.isEmpty)
              const Text(
                'لا توجد حالات متاحة للتغيير إليها',
                style: TextStyle(color: Colors.grey),
              )
            else
              Column(
                children:
                    _availableStatuses.map((status) {
                      return RadioListTile<OrderHandlingStatus>(
                        value: status,
                        groupValue: _selectedStatus,
                        onChanged: (OrderHandlingStatus? value) {
                          setState(() {
                            _selectedStatus = value;
                          });
                        },
                        title: Row(
                          children: [
                            Container(
                              width: 12,
                              height: 12,
                              decoration: BoxDecoration(
                                color: status.color,
                                shape: BoxShape.circle,
                              ),
                            ),
                            const SizedBox(width: 8),
                            Text(
                              status.displayName,
                              style: const TextStyle(fontSize: 14),
                            ),
                          ],
                        ),
                        contentPadding: EdgeInsets.zero,
                        dense: true,
                      );
                    }).toList(),
              ),

            const SizedBox(height: 16),
            const Text(
              'ملاحظة (اختيارية):',
              style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _noteController,
              maxLines: 3,
              decoration: InputDecoration(
                hintText: 'أضف ملاحظة حول تغيير الحالة...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: const EdgeInsets.all(12),
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(onPressed: () => Get.back(), child: const Text('إلغاء')),
        ElevatedButton(
          onPressed: _availableStatuses.isNotEmpty ? _handleStatusChange : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primary,
            foregroundColor: Colors.white,
          ),
          child: const Text('تغيير الحالة'),
        ),
      ],
    );
  }
}
