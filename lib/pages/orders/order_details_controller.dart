import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:myrunway/core/models/order_history_models.dart';
import 'package:myrunway/core/models/order_model_new.dart';
import 'package:myrunway/core/services/auth_service.dart';
import 'package:myrunway/core/services/order_service.dart';
import 'package:myrunway/core/constants/user_roles.dart';
import 'package:myrunway/pages/orders/widgets/transfer_order_dialog.dart';
import 'package:myrunway/pages/orders/widgets/change_status_dialog.dart';
import 'package:myrunway/pages/orders/widgets/delete_order_dialog.dart';
import 'package:myrunway/pages/orders/order_edit_page.dart';

class OrderDetailsController extends GetxController {
  final OrderService _orderService = Get.find<OrderService>();
  final AuthService _authService = Get.find<AuthService>();

  // Constructor parameter
  final int orderId;

  // Reactive variables
  final Rx<OrderWithHistory?> _orderWithHistory = Rx<OrderWithHistory?>(null);
  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;

  OrderDetailsController({required this.orderId});

  // Getters
  OrderWithHistory? get orderWithHistory => _orderWithHistory.value;
  bool get isLoading => _isLoading.value;
  String get error => _error.value;
  bool get hasError => _error.value.isNotEmpty;

  // Permission getters
  bool get canTransferOrder => _authService.hasPermission(UserRole.manager);
  bool get canChangeStatus => _authService.hasPermission(UserRole.manager);
  bool get canEditOrder => _authService.hasPermission(UserRole.manager);
  bool get canDeleteOrder => _authService.hasPermission(UserRole.master);

  @override
  void onInit() {
    super.onInit();
    loadOrderDetails(orderId);
  }

  // Load order details with history
  Future<void> loadOrderDetails(int orderId) async {
    _isLoading.value = true;
    _error.value = '';

    final response = await _orderService.getOrderWithHistory(orderId);

    if (response.success && response.data != null) {
      _orderWithHistory.value = response.data!;
    } else {
      _error.value = response.message ?? 'فشل في جلب تفاصيل الطلب';
      Get.snackbar(
        'خطأ',
        _error.value,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }

    _isLoading.value = false;
  }

  // Refresh order details
  Future<void> refreshOrderDetails() async {
    if (_orderWithHistory.value?.order.id != null) {
      await loadOrderDetails(_orderWithHistory.value!.order.id!);
    }
  }

  // Action methods with actual functionality
  void transferOrder() {
    if (!canTransferOrder) {
      Get.snackbar(
        'خطأ في الصلاحية',
        'ليس لديك صلاحية لنقل الطلبات',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    if (_orderWithHistory.value == null) return;

    Get.dialog(
      TransferOrderDialog(
        order: _orderWithHistory.value!.order,
        onTransfer: _handleTransferOrder,
      ),
    );
  }

  void changeOrderStatus() {
    if (!canChangeStatus) {
      Get.snackbar(
        'خطأ في الصلاحية',
        'ليس لديك صلاحية لتغيير حالة الطلبات',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    if (_orderWithHistory.value == null) return;

    Get.dialog(
      ChangeStatusDialog(
        order: _orderWithHistory.value!.order,
        onStatusChange: _handleStatusChange,
      ),
    );
  }

  void editOrder() {
    if (!canEditOrder) {
      Get.snackbar(
        'خطأ في الصلاحية',
        'ليس لديك صلاحية لتعديل الطلبات',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    if (_orderWithHistory.value == null) return;

    Get.to(() => OrderEditPage(order: _orderWithHistory.value!.order))?.then((
      r,
    ) {
      if (r == null) return;
      // Refresh order details after returning from edit page
      refreshOrderDetails();
    });
  }

  void deleteOrder() {
    if (!canDeleteOrder) {
      Get.snackbar(
        'خطأ في الصلاحية',
        'ليس لديك صلاحية لحذف الطلبات',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    if (_orderWithHistory.value == null) return;

    Get.dialog(
      DeleteOrderDialog(
        order: _orderWithHistory.value!.order,
        onConfirm: _handleDeleteOrder,
      ),
    );
  }

  void assignOrder() {
    Get.snackbar(
      'قريباً',
      'سيتم تطوير ميزة تعيين الطلب قريباً',
      backgroundColor: Colors.blue,
      colorText: Colors.white,
    );
    // TODO: Navigate to order assignment page
  }

  // Handler methods for actions
  Future<void> _handleTransferOrder(OrderTransferRequest request) async {
    if (_orderWithHistory.value == null) return;

    _isLoading.value = true;

    final response = await _orderService.transferOrder(
      _orderWithHistory.value!.order.id!,
      request,
    );

    if (response.success && response.data != null) {
      Get.snackbar(
        'نجح',
        'تم نقل الطلب بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
      // Refresh order details to show updated data
      await refreshOrderDetails();
    } else {
      Get.snackbar(
        'خطأ',
        response.message ?? 'فشل في نقل الطلب',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }

    _isLoading.value = false;
  }

  Future<void> _handleStatusChange(OrderStatusUpdateRequest request) async {
    if (_orderWithHistory.value == null) return;

    _isLoading.value = true;

    final response = await _orderService.updateOrderStatus(
      _orderWithHistory.value!.order.id!,
      request,
    );

    if (response.success && response.data != null) {
      Get.snackbar(
        'نجح',
        'تم تحديث حالة الطلب بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
      // Refresh order details to show updated data
      await refreshOrderDetails();
    } else {
      Get.snackbar(
        'خطأ',
        response.message ?? 'فشل في تحديث حالة الطلب',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }

    _isLoading.value = false;
  }

  Future<void> _handleDeleteOrder() async {
    if (_orderWithHistory.value == null) return;

    _isLoading.value = true;

    final response = await _orderService.deleteOrder(
      _orderWithHistory.value!.order.id!,
    );

    if (response.success) {
      Get.snackbar(
        'نجح',
        'تم حذف الطلب بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
      // Navigate back to orders list
      Get.back();
    } else {
      Get.snackbar(
        'خطأ',
        response.message ?? 'فشل في حذف الطلب',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }

    _isLoading.value = false;
  }

  // Helper methods for timeline data
  List<TimelineEvent> getTimelineEvents() {
    if (_orderWithHistory.value == null) return [];

    final events = <TimelineEvent>[];
    final order = _orderWithHistory.value!.order;

    // Add order creation event
    events.add(
      TimelineEvent(
        title: 'تم إنشاء الطلب',
        description: 'تم إنشاء الطلب #${order.code}',
        timestamp: order.createdAt,
        type: TimelineEventType.created,
        icon: Icons.add_circle,
      ),
    );

    // Add assignment history events
    for (final assignment in _orderWithHistory.value!.assigningHistory) {
      events.add(
        TimelineEvent(
          title: 'تم تعيين الطلب',
          description:
              'تم تعيين الطلب إلى ${assignment.assignedTo.fullName} بواسطة ${assignment.assignedBy.fullName}',
          timestamp: assignment.assignedAt,
          type: TimelineEventType.assigned,
          icon: Icons.person_add,
        ),
      );
    }

    // Add status history events
    for (final statusChange in _orderWithHistory.value!.handlingStatusHistory) {
      events.add(
        TimelineEvent(
          title: 'تم تغيير الحالة',
          description:
              statusChange.handlingStatus != null
                  ? 'تم تغيير حالة المعالجة إلى ${statusChange.handlingStatus!.displayName}'
                  : 'تم تحديث حالة الطلب',
          timestamp: statusChange.createdAt,
          type: TimelineEventType.statusChanged,
          icon: Icons.update,
          note: statusChange.note,
        ),
      );
    }

    // Add proof events
    for (final proof in _orderWithHistory.value!.proofs) {
      events.add(
        TimelineEvent(
          title: 'تم إضافة إثبات',
          description:
              '${proof.proofType.displayName} بواسطة ${proof.proofBy.fullName}',
          timestamp: proof.createdAt,
          type: TimelineEventType.proofAdded,
          icon: Icons.camera_alt,
          hasImage: proof.hasImage,
          hasLocation: proof.hasLocation,
        ),
      );
    }

    // Sort events by timestamp (newest first)
    events.sort((a, b) => b.timestamp.compareTo(a.timestamp));

    return events;
  }
}

// Timeline event model for UI
class TimelineEvent {
  final String title;
  final String description;
  final DateTime timestamp;
  final TimelineEventType type;
  final IconData icon;
  final String? note;
  final bool hasImage;
  final bool hasLocation;

  TimelineEvent({
    required this.title,
    required this.description,
    required this.timestamp,
    required this.type,
    required this.icon,
    this.note,
    this.hasImage = false,
    this.hasLocation = false,
  });
}

enum TimelineEventType { created, assigned, statusChanged, proofAdded }

extension TimelineEventTypeExtension on TimelineEventType {
  Color get color {
    switch (this) {
      case TimelineEventType.created:
        return Colors.blue;
      case TimelineEventType.assigned:
        return Colors.orange;
      case TimelineEventType.statusChanged:
        return Colors.purple;
      case TimelineEventType.proofAdded:
        return Colors.green;
    }
  }
}
