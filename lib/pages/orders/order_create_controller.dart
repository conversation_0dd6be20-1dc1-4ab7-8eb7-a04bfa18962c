import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:myrunway/core/models/order_model_new.dart';
import 'package:myrunway/core/models/company_model.dart';
import 'package:myrunway/core/services/auth_service.dart';
import 'package:myrunway/core/services/order_service.dart';
import 'package:myrunway/core/services/company_service.dart';
import 'package:myrunway/core/constants/user_roles.dart';

class OrderCreateController extends GetxController {
  final OrderService _orderService = Get.find<OrderService>();
  final CompanyService _companyService = Get.find<CompanyService>();
  final AuthService _authService = Get.find<AuthService>();

  // Reactive variables
  final RxBool _isCreating = false.obs;
  final RxList<CompanyModel> _companies = <CompanyModel>[].obs;
  final RxBool _isLoadingCompanies = false.obs;

  // Getters
  bool get isCreating => _isCreating.value;
  List<CompanyModel> get companies => _companies;
  bool get isLoadingCompanies => _isLoadingCompanies.value;

  // Permission getters
  bool get canCreateOrders => _authService.hasPermission(UserRole.employee);

  @override
  void onInit() {
    super.onInit();
    loadCompanies();
  }

  // Load companies for dropdown
  Future<void> loadCompanies() async {
    _isLoadingCompanies.value = true;

    final response = await _companyService.getCompanies();

    if (response.success && response.data != null) {
      _companies.value = response.data!;
    } else {
      Get.snackbar(
        'خطأ',
        response.message ?? 'فشل في جلب قائمة الشركات',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }

    _isLoadingCompanies.value = false;
  }

  // Create a new order
  Future<bool> createOrder(OrderCreateRequest request) async {
    if (!canCreateOrders) {
      Get.snackbar(
        'خطأ في الصلاحية',
        'ليس لديك صلاحية لإنشاء طلب جديد',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }

    _isCreating.value = true;

    final response = await _orderService.createOrder(request);

    if (response.success && response.data != null) {
      Get.snackbar(
        'نجح',
        'تم إنشاء الطلب بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
      _isCreating.value = false;
      return true;
    } else {
      Get.snackbar(
        'خطأ',
        response.message ?? 'فشل في إنشاء الطلب',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      _isCreating.value = false;
      return false;
    }
  }
}
