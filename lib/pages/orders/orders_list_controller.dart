import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:myrunway/core/models/order_model_new.dart';
import 'package:myrunway/core/services/auth_service.dart';
import 'package:myrunway/core/services/order_service.dart';
import 'package:myrunway/core/constants/user_roles.dart';

class OrdersListController extends GetxController {
  final OrderService _orderService = Get.find<OrderService>();
  final AuthService _authService = Get.find<AuthService>();

  // Reactive variables
  final RxList<OrderModelNew> _orders = <OrderModelNew>[].obs;
  final RxList<OrderModelNew> _myOrders = <OrderModelNew>[].obs;
  final RxBool _isLoading = false.obs;
  final Rx<OrderModelNew?> _selectedOrder = Rx<OrderModelNew?>(null);

  // Getters
  List<OrderModelNew> get orders => _orders;
  List<OrderModelNew> get myOrders => _myOrders;
  bool get isLoading => _isLoading.value;
  OrderModelNew? get selectedOrder => _selectedOrder.value;

  // Permission getters
  bool get canViewAllOrders => _authService.hasPermission(UserRole.manager);
  bool get canCreateOrders => _authService.hasPermission(UserRole.employee);
  bool get canEditOrders => _authService.hasPermission(UserRole.manager);
  bool get canDeleteOrders => _authService.hasPermission(UserRole.master);

  @override
  void onInit() {
    super.onInit();
    if (canViewAllOrders) {
      loadOrders();
    } else {
      loadMyOrders();
    }
  }

  // Load all orders (for managers and masters)
  Future<void> loadOrders({
    String? dateFrom,
    String? dateTo,
    String? status,
    int? assignedTo,
  }) async {
    if (!canViewAllOrders) {
      Get.snackbar(
        'خطأ في الصلاحية',
        'ليس لديك صلاحية لعرض جميع الطلبات',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    _isLoading.value = true;

    final response = await _orderService.getOrders(
      dateFrom: dateFrom,
      dateTo: dateTo,
      status: status,
      assignedTo: assignedTo,
    );

    if (response.success && response.data != null) {
      _orders.value = response.data!;
    } else {
      Get.snackbar(
        'خطأ',
        response.message ?? 'فشل في جلب قائمة الطلبات',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }

    _isLoading.value = false;
  }

  // Load my orders (for employees)
  Future<void> loadMyOrders({
    String? dateFrom,
    String? dateTo,
    String? status,
  }) async {
    _isLoading.value = true;

    final response = await _orderService.getMyOrders(
      dateFrom: dateFrom,
      dateTo: dateTo,
      status: status,
    );

    if (response.success && response.data != null) {
      _myOrders.value = response.data!;
    } else {
      Get.snackbar(
        'خطأ',
        response.message ?? 'فشل في جلب طلباتي',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }

    _isLoading.value = false;
  }

  // Delete an order
  Future<bool> deleteOrder(int orderId) async {
    if (!canDeleteOrders) {
      Get.snackbar(
        'خطأ في الصلاحية',
        'ليس لديك صلاحية لحذف الطلبات',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }

    final response = await _orderService.deleteOrder(orderId);

    if (response.success) {
      _orders.removeWhere((order) => order.id == orderId);
      _myOrders.removeWhere((order) => order.id == orderId);
      Get.snackbar(
        'نجح',
        'تم حذف الطلب بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
      return true;
    } else {
      Get.snackbar(
        'خطأ',
        response.message ?? 'فشل في حذف الطلب',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }
  }

  // Select an order
  void selectOrder(OrderModelNew order) {
    _selectedOrder.value = order;
  }

  // Clear selection
  void clearSelection() {
    _selectedOrder.value = null;
  }

  // Refresh orders
  Future<void> refreshOrders() async {
    if (canViewAllOrders) {
      await loadOrders();
    } else {
      await loadMyOrders();
    }
  }
}
