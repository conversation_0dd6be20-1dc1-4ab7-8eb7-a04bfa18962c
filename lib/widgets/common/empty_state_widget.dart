import 'package:flutter/material.dart';
import 'package:myrunway/core/constants/app_colors.dart';

class EmptyStateWidget extends StatelessWidget {
  final IconData icon;
  final String title;
  final String? subtitle;
  final String? actionText;
  final VoidCallback? onActionPressed;
  final Color? iconColor;
  final double iconSize;
  final Widget? customIcon;

  const EmptyStateWidget({
    super.key,
    required this.icon,
    required this.title,
    this.subtitle,
    this.actionText,
    this.onActionPressed,
    this.iconColor,
    this.iconSize = 80,
    this.customIcon,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            customIcon ??
                Icon(
                  icon,
                  size: iconSize,
                  color: iconColor ?? AppColors.grey400,
                ),
            const SizedBox(height: 24),
            Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
              textAlign: TextAlign.center,
            ),
            if (subtitle != null) ...[
              const SizedBox(height: 8),
              Text(
                subtitle!,
                style: const TextStyle(
                  fontSize: 14,
                  color: AppColors.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),
            ],
            if (actionText != null && onActionPressed != null) ...[
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: onActionPressed,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text(actionText!),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

class NoOrdersWidget extends StatelessWidget {
  final String? message;
  final VoidCallback? onRefresh;

  const NoOrdersWidget({
    super.key,
    this.message,
    this.onRefresh,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      icon: Icons.shopping_bag_outlined,
      title: 'لا توجد طلبات',
      subtitle: message ?? 'لم يتم العثور على أي طلبات في الوقت الحالي',
      actionText: onRefresh != null ? 'تحديث' : null,
      onActionPressed: onRefresh,
      iconColor: AppColors.info,
    );
  }
}

class NoEmployeesWidget extends StatelessWidget {
  final VoidCallback? onAddEmployee;

  const NoEmployeesWidget({
    super.key,
    this.onAddEmployee,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      icon: Icons.people_outline,
      title: 'لا يوجد موظفين',
      subtitle: 'لم يتم إضافة أي موظفين بعد',
      actionText: onAddEmployee != null ? 'إضافة موظف' : null,
      onActionPressed: onAddEmployee,
      iconColor: AppColors.success,
    );
  }
}

class NoClientsWidget extends StatelessWidget {
  final VoidCallback? onAddClient;

  const NoClientsWidget({
    super.key,
    this.onAddClient,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      icon: Icons.business_outlined,
      title: 'لا يوجد عملاء',
      subtitle: 'لم يتم إضافة أي عملاء بعد',
      actionText: onAddClient != null ? 'إضافة عميل' : null,
      onActionPressed: onAddClient,
      iconColor: AppColors.warning,
    );
  }
}

class NoOfficesWidget extends StatelessWidget {
  final VoidCallback? onAddOffice;

  const NoOfficesWidget({
    super.key,
    this.onAddOffice,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      icon: Icons.business_center_outlined,
      title: 'لا توجد مكاتب',
      subtitle: 'لم يتم إضافة أي مكاتب بعد',
      actionText: onAddOffice != null ? 'إضافة مكتب' : null,
      onActionPressed: onAddOffice,
      iconColor: AppColors.primary,
    );
  }
}

class NoReportsWidget extends StatelessWidget {
  final VoidCallback? onGenerateReport;

  const NoReportsWidget({
    super.key,
    this.onGenerateReport,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      icon: Icons.analytics_outlined,
      title: 'لا توجد تقارير',
      subtitle: 'لم يتم إنشاء أي تقارير بعد',
      actionText: onGenerateReport != null ? 'إنشاء تقرير' : null,
      onActionPressed: onGenerateReport,
      iconColor: AppColors.info,
    );
  }
}

class NoSearchResultsWidget extends StatelessWidget {
  final String searchQuery;
  final VoidCallback? onClearSearch;

  const NoSearchResultsWidget({
    super.key,
    required this.searchQuery,
    this.onClearSearch,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      icon: Icons.search_off,
      title: 'لا توجد نتائج',
      subtitle: 'لم يتم العثور على نتائج للبحث عن "$searchQuery"',
      actionText: onClearSearch != null ? 'مسح البحث' : null,
      onActionPressed: onClearSearch,
      iconColor: AppColors.grey400,
    );
  }
}

class ErrorStateWidget extends StatelessWidget {
  final String title;
  final String? subtitle;
  final VoidCallback? onRetry;
  final IconData? icon;

  const ErrorStateWidget({
    super.key,
    required this.title,
    this.subtitle,
    this.onRetry,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      icon: icon ?? Icons.error_outline,
      title: title,
      subtitle: subtitle,
      actionText: onRetry != null ? 'إعادة المحاولة' : null,
      onActionPressed: onRetry,
      iconColor: AppColors.error,
    );
  }
}

class NetworkErrorWidget extends StatelessWidget {
  final VoidCallback? onRetry;

  const NetworkErrorWidget({
    super.key,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return ErrorStateWidget(
      title: 'خطأ في الاتصال',
      subtitle: 'تحقق من اتصالك بالإنترنت وحاول مرة أخرى',
      onRetry: onRetry,
      icon: Icons.wifi_off,
    );
  }
}

class ServerErrorWidget extends StatelessWidget {
  final VoidCallback? onRetry;

  const ServerErrorWidget({
    super.key,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return ErrorStateWidget(
      title: 'خطأ في الخادم',
      subtitle: 'حدث خطأ في الخادم، يرجى المحاولة لاحقاً',
      onRetry: onRetry,
      icon: Icons.cloud_off,
    );
  }
}

class PermissionDeniedWidget extends StatelessWidget {
  final String? message;

  const PermissionDeniedWidget({
    super.key,
    this.message,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      icon: Icons.lock_outline,
      title: 'ليس لديك صلاحية',
      subtitle: message ?? 'ليس لديك صلاحية للوصول إلى هذه الصفحة',
      iconColor: AppColors.warning,
    );
  }
}

class MaintenanceWidget extends StatelessWidget {
  final String? message;

  const MaintenanceWidget({
    super.key,
    this.message,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      icon: Icons.build_outlined,
      title: 'صيانة مؤقتة',
      subtitle: message ?? 'النظام تحت الصيانة، يرجى المحاولة لاحقاً',
      iconColor: AppColors.warning,
    );
  }
}
