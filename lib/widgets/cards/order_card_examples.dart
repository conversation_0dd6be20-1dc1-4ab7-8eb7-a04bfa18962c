import 'package:flutter/material.dart';
import 'package:myrunway/widgets/cards/order_card.dart';
import 'package:myrunway/core/models/order_model.dart';
import 'package:myrunway/core/constants/app_colors.dart';

/// Example usage of the OrderCard widget with different configurations
/// This file demonstrates various ways to use the reusable OrderCard component
class OrderCardExamples extends StatelessWidget {
  const OrderCardExamples({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('OrderCard Examples'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionTitle('1. Standard OrderCard (Old Model)'),
            _buildStandardOrderCard(),

            const SizedBox(height: 24),
            _buildSectionTitle('2. Compact OrderCard'),
            _buildCompactOrderCard(),

            const SizedBox(height: 24),
            _buildSectionTitle('3. Assignment Mode with Selection'),
            _buildAssignmentOrderCard(),

            const SizedBox(height: 24),
            _buildSectionTitle('4. Management Mode with Actions'),
            _buildManagementOrderCard(),

            const SizedBox(height: 24),
            _buildSectionTitle('5. OrderCard with New Model'),
            _buildNewModelOrderCard(),

            const SizedBox(height: 24),
            _buildSectionTitle('6. Custom Styled OrderCard'),
            _buildCustomStyledOrderCard(),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: AppColors.primary,
        ),
      ),
    );
  }

  /// Example 1: Standard OrderCard using the old Order model
  Widget _buildStandardOrderCard() {
    final order = Order(
      id: 'ORD-001',
      customerName: 'أحمد محمد علي',
      customerPhone: '+966501234567',
      totalAmount: 1250.50,
      assignedDate: DateTime.now().subtract(const Duration(days: 1)),
      assignee: 'سارة أحمد',
      status: OrderStatus.pending,
      handlingStatus: HandlingStatus.assigned,
      address: 'الرياض، حي النخيل، شارع الملك فهد',
      itemsCount: 3,
      collectedAmount: 0.0,
      commissionRate: 0.05,
      commissionAmount: 0.0,
    );

    return OrderCard(
      order: order,
      onTap: () => _showSnackBar('تم النقر على الطلب ${order.id}'),
      config: const OrderCardConfig(displayMode: OrderCardDisplayMode.standard),
    );
  }

  /// Example 2: Compact OrderCard for dense lists
  Widget _buildCompactOrderCard() {
    final order = Order(
      id: 'ORD-002',
      customerName: 'فاطمة أحمد',
      customerPhone: '+966501234568',
      totalAmount: 850.25,
      assignedDate: DateTime.now(),
      assignee: 'محمد علي',
      status: OrderStatus.processing,
      handlingStatus: HandlingStatus.inProgress,
      address: 'جدة، حي الروضة',
      itemsCount: 2,
      collectedAmount: 0.0,
      commissionRate: 0.05,
      commissionAmount: 0.0,
    );

    return OrderCard(
      order: order,
      onTap: () => _showSnackBar('تم النقر على الطلب المضغوط ${order.id}'),
      config: const OrderCardConfig(
        displayMode: OrderCardDisplayMode.compact,
        margin: EdgeInsets.only(bottom: 8),
        padding: EdgeInsets.all(12),
      ),
    );
  }

  /// Example 3: Assignment mode with selection capability
  Widget _buildAssignmentOrderCard() {
    final order = Order(
      id: 'ORD-003',
      customerName: 'خالد سعد',
      customerPhone: '+966501234569',
      totalAmount: 2100.00,
      assignedDate: DateTime.now().subtract(const Duration(hours: 2)),
      assignee: 'علي محمد',
      status: OrderStatus.shipped,
      handlingStatus: HandlingStatus.inProgress,
      address: 'الدمام، حي الفيصلية',
      itemsCount: 5,
      collectedAmount: 0.0,
      commissionRate: 0.05,
      commissionAmount: 0.0,
    );

    return OrderCard(
      order: order,
      onSelectionChanged:
          () => _showSnackBar('تم تغيير تحديد الطلب ${order.id}'),
      config: const OrderCardConfig(
        displayMode: OrderCardDisplayMode.assignment,
        showSelection: true,
        isSelected: true,
      ),
    );
  }

  /// Example 4: Management mode with action buttons
  Widget _buildManagementOrderCard() {
    final order = Order(
      id: 'ORD-004',
      customerName: 'نورا عبدالله',
      customerPhone: '+966501234570',
      totalAmount: 1750.75,
      assignedDate: DateTime.now().subtract(const Duration(days: 3)),
      assignee: 'سعد أحمد',
      status: OrderStatus.delivered,
      handlingStatus: HandlingStatus.completed,
      address: 'الرياض، حي العليا',
      itemsCount: 4,
      collectedAmount: 1750.75,
      commissionRate: 0.05,
      commissionAmount: 87.54,
    );

    return OrderCard(
      order: order,
      onTap: () => _showSnackBar('تم النقر على طلب الإدارة ${order.id}'),
      config: OrderCardConfig(
        displayMode: OrderCardDisplayMode.management,
        showActions: true,
        actionButtons: [
          IconButton(
            icon: const Icon(Icons.edit, size: 20),
            onPressed: () => _showSnackBar('تعديل الطلب ${order.id}'),
            color: AppColors.primary,
          ),
          IconButton(
            icon: const Icon(Icons.transfer_within_a_station, size: 20),
            onPressed: () => _showSnackBar('نقل الطلب ${order.id}'),
            color: Colors.orange,
          ),
          IconButton(
            icon: const Icon(Icons.delete, size: 20),
            onPressed: () => _showSnackBar('حذف الطلب ${order.id}'),
            color: Colors.red,
          ),
        ],
      ),
    );
  }

  /// Example 5: OrderCard using the new OrderModelNew
  Widget _buildNewModelOrderCard() {
    // Note: This is a simplified example. In real usage, you would have proper model instances
    // For demonstration purposes, we'll use the old model but show how the new model would work
    final order = Order(
      id: 'NEW-001',
      customerName: 'عبدالرحمن محمد',
      customerPhone: '+966501234571',
      totalAmount: 3200.00,
      assignedDate: DateTime.now(),
      assignee: 'ريم سعد',
      status: OrderStatus.pending,
      handlingStatus: HandlingStatus.assigned,
      address: 'مكة المكرمة، حي العزيزية',
      itemsCount: 8,
      collectedAmount: 0.0,
      commissionRate: 0.05,
      commissionAmount: 0.0,
    );

    return OrderCard(
      order: order,
      onTap: () => _showSnackBar('تم النقر على الطلب الجديد ${order.id}'),
      config: const OrderCardConfig(displayMode: OrderCardDisplayMode.standard),
    );
  }

  /// Example 6: Custom styled OrderCard
  Widget _buildCustomStyledOrderCard() {
    final order = Order(
      id: 'CUSTOM-001',
      customerName: 'مريم عبدالعزيز',
      customerPhone: '+966501234572',
      totalAmount: 950.50,
      assignedDate: DateTime.now().subtract(const Duration(hours: 6)),
      assignee: 'أحمد سالم',
      status: OrderStatus.cancelled,
      handlingStatus: HandlingStatus.onHold,
      address: 'الطائف، حي الشفا',
      itemsCount: 2,
      collectedAmount: 0.0,
      commissionRate: 0.05,
      commissionAmount: 0.0,
    );

    return OrderCard(
      order: order,
      onTap: () => _showSnackBar('تم النقر على الطلب المخصص ${order.id}'),
      config: OrderCardConfig(
        displayMode: OrderCardDisplayMode.standard,
        backgroundColor: Colors.red.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        margin: const EdgeInsets.only(bottom: 12),
        boxShadow: [
          BoxShadow(
            color: Colors.red.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
    );
  }

  void _showSnackBar(String message) {
    // In a real app, you would use Get.snackbar or ScaffoldMessenger
    print(message); // For demonstration purposes
  }
}

/// Usage Examples in Different Contexts:
/// 
/// 1. Employee Orders List:
/// ```dart
/// OrderCard(
///   order: order,
///   onTap: () => controller.onOrderTap(index),
/// )
/// ```
/// 
/// 2. Order Assignment Page:
/// ```dart
/// OrderCard(
///   orderNew: order,
///   onSelectionChanged: () => controller.toggleOrderSelection(order.id!),
///   config: OrderCardConfig(
///     displayMode: OrderCardDisplayMode.assignment,
///     showSelection: true,
///     isSelected: controller.selectedOrderIds.contains(order.id),
///   ),
/// )
/// ```
/// 
/// 3. Order Management with Actions:
/// ```dart
/// OrderCard(
///   orderNew: order,
///   onTap: () => Get.to(() => OrderDetailsPage(orderId: order.id!)),
///   config: OrderCardConfig(
///     displayMode: OrderCardDisplayMode.management,
///     showActions: true,
///     actionButtons: [
///       IconButton(
///         icon: Icon(Icons.edit),
///         onPressed: () => editOrder(order),
///       ),
///       IconButton(
///         icon: Icon(Icons.delete),
///         onPressed: () => deleteOrder(order),
///       ),
///     ],
///   ),
/// )
/// ```
