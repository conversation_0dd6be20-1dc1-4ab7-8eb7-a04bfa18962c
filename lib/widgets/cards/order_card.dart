import 'package:flutter/material.dart';
import 'package:myrunway/core/models/order_model.dart';
import 'package:myrunway/core/models/order_model_new.dart';
import 'package:myrunway/utils/status_utils.dart';

/// Configuration for OrderCard display modes and styling
enum OrderCardDisplayMode {
  /// Standard display mode for employee orders list
  standard,

  /// Compact mode for dense lists
  compact,

  /// Assignment mode with selection capabilities
  assignment,

  /// Management mode with action buttons
  management,
}

/// Configuration class for OrderCard customization
class OrderCardConfig {
  final OrderCardDisplayMode displayMode;
  final bool showActions;
  final bool showSelection;
  final bool isSelected;
  final List<Widget>? actionButtons;
  final Color? backgroundColor;
  final EdgeInsets? margin;
  final EdgeInsets? padding;
  final BorderRadius? borderRadius;
  final List<BoxShadow>? boxShadow;

  const OrderCardConfig({
    this.displayMode = OrderCardDisplayMode.standard,
    this.showActions = false,
    this.showSelection = false,
    this.isSelected = false,
    this.actionButtons,
    this.backgroundColor,
    this.margin,
    this.padding,
    this.borderRadius,
    this.boxShadow,
  });

  OrderCardConfig copyWith({
    OrderCardDisplayMode? displayMode,
    bool? showActions,
    bool? showSelection,
    bool? isSelected,
    List<Widget>? actionButtons,
    Color? backgroundColor,
    EdgeInsets? margin,
    EdgeInsets? padding,
    BorderRadius? borderRadius,
    List<BoxShadow>? boxShadow,
  }) {
    return OrderCardConfig(
      displayMode: displayMode ?? this.displayMode,
      showActions: showActions ?? this.showActions,
      showSelection: showSelection ?? this.showSelection,
      isSelected: isSelected ?? this.isSelected,
      actionButtons: actionButtons ?? this.actionButtons,
      backgroundColor: backgroundColor ?? this.backgroundColor,
      margin: margin ?? this.margin,
      padding: padding ?? this.padding,
      borderRadius: borderRadius ?? this.borderRadius,
      boxShadow: boxShadow ?? this.boxShadow,
    );
  }
}

/// Reusable OrderCard widget that supports both Order and OrderModelNew models
/// with flexible configuration for different display contexts
class OrderCard extends StatelessWidget {
  // Order data - supports both model types
  final Order? order;
  final OrderModelNew? orderNew;

  // Interaction callbacks
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final VoidCallback? onSelectionChanged;

  // Configuration
  final OrderCardConfig config;

  const OrderCard({
    super.key,
    this.order,
    this.orderNew,
    this.onTap,
    this.onLongPress,
    this.onSelectionChanged,
    this.config = const OrderCardConfig(),
  }) : assert(
         (order != null) ^ (orderNew != null),
         'Either order or orderNew must be provided, but not both',
       );

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: config.margin ?? const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: _getBackgroundColor(),
        borderRadius: config.borderRadius ?? BorderRadius.circular(20),
        boxShadow: config.boxShadow ?? _getDefaultShadow(),
        border:
            config.isSelected
                ? Border.all(color: const Color(0xFF667eea), width: 2)
                : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: config.borderRadius ?? BorderRadius.circular(20),
          onTap: config.showSelection ? onSelectionChanged : onTap,
          onLongPress: onLongPress,
          child: Padding(
            padding: config.padding ?? const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildHeader(),
                if (config.displayMode != OrderCardDisplayMode.compact) ...[
                  const SizedBox(height: 16),
                  _buildCustomerInfo(),
                  const SizedBox(height: 16),
                  _buildFooter(),
                ],
                if (config.showActions && config.actionButtons != null) ...[
                  const SizedBox(height: 12),
                  _buildActionButtons(),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Color _getBackgroundColor() {
    if (config.backgroundColor != null) {
      return config.backgroundColor!;
    }
    if (config.isSelected) {
      return const Color(0xFF667eea).withValues(alpha: 0.05);
    }
    return Colors.white;
  }

  List<BoxShadow> _getDefaultShadow() {
    return [
      BoxShadow(
        color: const Color(0xFF667eea).withValues(alpha: 0.08),
        blurRadius: 20,
        offset: const Offset(0, 8),
      ),
    ];
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              colors: [Color(0xFF667eea), Color(0xFF764ba2)],
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            _getOrderId(),
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
          ),
        ),
        const Spacer(),
        if (config.showSelection && config.isSelected)
          const Icon(Icons.check_circle, color: Color(0xFF667eea), size: 24),
        if (config.showSelection && config.isSelected) const SizedBox(width: 8),
        ..._buildStatusBadges(),
      ],
    );
  }

  List<Widget> _buildStatusBadges() {
    if (order != null) {
      return [
        StatusBadge(
          status: order!.status,
          color: StatusUtils.getStatusColor(order!.status),
          text: StatusUtils.getStatusText(order!.status),
        ),
        const SizedBox(width: 8),
        StatusBadge(
          status: order!.handlingStatus,
          color: StatusUtils.getHandlingStatusColor(order!.handlingStatus),
          text: StatusUtils.getHandlingStatusText(order!.handlingStatus),
        ),
      ];
    } else if (orderNew != null) {
      return [
        StatusBadge(
          status: orderNew!.orderHandlingStatus,
          color: orderNew!.orderHandlingStatus.color,
          text: orderNew!.orderHandlingStatus.displayName,
        ),
        if (orderNew!.orderDeliveryStatus != null) ...[
          const SizedBox(width: 8),
          StatusBadge(
            status: orderNew!.orderDeliveryStatus!,
            color: const Color(
              0xFF3B82F6,
            ), // Default blue color for delivery status
            text: orderNew!.orderDeliveryStatus!.name,
          ),
        ],
      ];
    }
    return [];
  }

  Widget _buildCustomerInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          _buildInfoRow(
            icon: Icons.person_rounded,
            iconColor: const Color(0xFF667eea),
            title: _getCustomerName(),
            subtitle: _getCustomerPhone(),
          ),
          const SizedBox(height: 12),
          _buildInfoRow(
            icon: Icons.location_on_rounded,
            iconColor: const Color(0xFFE74C3C),
            title: _getCustomerAddress(),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow({
    required IconData icon,
    required Color iconColor,
    required String title,
    String? subtitle,
  }) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: iconColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: iconColor, size: 20),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                  fontSize: subtitle != null ? 16 : 14,
                  fontWeight:
                      subtitle != null ? FontWeight.w600 : FontWeight.normal,
                  color: const Color(0xFF2C3E50),
                  height: 1.4,
                ),
              ),
              if (subtitle != null) ...[
                const SizedBox(height: 2),
                Text(
                  subtitle,
                  style: const TextStyle(
                    color: Color(0xFF7F8C8D),
                    fontSize: 14,
                  ),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildFooter() {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (_getAssignee() != null) ...[
                Row(
                  children: [
                    const Icon(
                      Icons.person_pin_rounded,
                      size: 16,
                      color: Color(0xFF7F8C8D),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'المسؤول: ${_getAssignee()}',
                      style: const TextStyle(
                        color: Color(0xFF7F8C8D),
                        fontSize: 13,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
              ],
              Row(
                children: [
                  const Icon(
                    Icons.schedule_rounded,
                    size: 16,
                    color: Color(0xFF7F8C8D),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    _formatDate(_getDate()),
                    style: const TextStyle(
                      color: Color(0xFF7F8C8D),
                      fontSize: 13,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              colors: [Color(0xFF27AE60), Color(0xFF2ECC71)],
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                '${_getTotalAmount().toStringAsFixed(2)} جنيه',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              if (_getItemsCount() != null)
                Text(
                  '${_getItemsCount()} عنصر',
                  style: const TextStyle(color: Colors.white70, fontSize: 12),
                ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: config.actionButtons!,
    );
  }

  // Helper methods to extract data from either model type
  String _getOrderId() {
    if (order != null) return order!.id;
    if (orderNew != null) return orderNew!.code;
    return '';
  }

  String _getCustomerName() {
    if (order != null) return order!.customerName;
    if (orderNew != null) return orderNew!.customerName;
    return '';
  }

  String _getCustomerPhone() {
    if (order != null) return order!.customerPhone;
    if (orderNew != null) return orderNew!.customerPhone;
    return '';
  }

  String _getCustomerAddress() {
    if (order != null) return order!.address;
    if (orderNew != null) return orderNew!.customerAddress ?? '';
    return '';
  }

  String? _getAssignee() {
    if (order != null) return order!.assignee;
    if (orderNew != null) return orderNew!.assignedTo?.name;
    return null;
  }

  DateTime _getDate() {
    if (order != null) return order!.assignedDate;
    if (orderNew != null) return orderNew!.assignedAt ?? orderNew!.createdAt;
    return DateTime.now();
  }

  double _getTotalAmount() {
    if (order != null) return order!.totalAmount;
    if (orderNew != null) return orderNew!.totalPrice ?? 0.0;
    return 0.0;
  }

  int? _getItemsCount() {
    if (order != null) return order!.itemsCount;
    // OrderModelNew doesn't have itemsCount, return null
    return null;
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

/// Status badge widget for displaying order status
class StatusBadge extends StatelessWidget {
  final dynamic status;
  final Color color;
  final String text;

  const StatusBadge({
    super.key,
    required this.status,
    required this.color,
    required this.text,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3), width: 1),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: color,
          fontSize: 11,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }
}
