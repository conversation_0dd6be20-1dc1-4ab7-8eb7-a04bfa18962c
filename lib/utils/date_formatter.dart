import 'package:intl/intl.dart';

class DateFormatter {
  // Arabic date formatter
  static final DateFormat _arabicDateFormat = DateFormat('dd/MM/yyyy', 'ar');
  static final DateFormat _arabicTimeFormat = DateFormat('HH:mm', 'ar');
  static final DateFormat _arabicDateTimeFormat = DateFormat('dd/MM/yyyy HH:mm', 'ar');
  static final DateFormat _arabicDayFormat = DateFormat('EEEE', 'ar');
  static final DateFormat _arabicMonthFormat = DateFormat('MMMM yyyy', 'ar');

  /// Format date to Arabic format (dd/MM/yyyy)
  static String formatDate(DateTime date) {
    return _arabicDateFormat.format(date);
  }

  /// Format time to Arabic format (HH:mm)
  static String formatTime(DateTime date) {
    return _arabicTimeFormat.format(date);
  }

  /// Format date and time to Arabic format (dd/MM/yyyy HH:mm)
  static String formatDateTime(DateTime date) {
    return _arabicDateTimeFormat.format(date);
  }

  /// Format day name in Arabic (e.g., الأحد، الاثنين)
  static String formatDayName(DateTime date) {
    return _arabicDayFormat.format(date);
  }

  /// Format month and year in Arabic (e.g., يناير 2024)
  static String formatMonthYear(DateTime date) {
    return _arabicMonthFormat.format(date);
  }

  /// Get relative time in Arabic (e.g., منذ 5 دقائق، منذ ساعة)
  static String getRelativeTime(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inSeconds < 60) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      final minutes = difference.inMinutes;
      return 'منذ $minutes ${minutes == 1 ? 'دقيقة' : 'دقيقة'}';
    } else if (difference.inHours < 24) {
      final hours = difference.inHours;
      return 'منذ $hours ${hours == 1 ? 'ساعة' : 'ساعة'}';
    } else if (difference.inDays < 7) {
      final days = difference.inDays;
      return 'منذ $days ${days == 1 ? 'يوم' : 'أيام'}';
    } else if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      return 'منذ $weeks ${weeks == 1 ? 'أسبوع' : 'أسابيع'}';
    } else if (difference.inDays < 365) {
      final months = (difference.inDays / 30).floor();
      return 'منذ $months ${months == 1 ? 'شهر' : 'أشهر'}';
    } else {
      final years = (difference.inDays / 365).floor();
      return 'منذ $years ${years == 1 ? 'سنة' : 'سنوات'}';
    }
  }

  /// Get time of day greeting in Arabic
  static String getTimeGreeting() {
    final hour = DateTime.now().hour;
    
    if (hour >= 5 && hour < 12) {
      return 'صباح الخير';
    } else if (hour >= 12 && hour < 17) {
      return 'مساء الخير';
    } else if (hour >= 17 && hour < 21) {
      return 'مساء الخير';
    } else {
      return 'مساء الخير';
    }
  }

  /// Check if date is today
  static bool isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year && 
           date.month == now.month && 
           date.day == now.day;
  }

  /// Check if date is yesterday
  static bool isYesterday(DateTime date) {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return date.year == yesterday.year && 
           date.month == yesterday.month && 
           date.day == yesterday.day;
  }

  /// Check if date is this week
  static bool isThisWeek(DateTime date) {
    final now = DateTime.now();
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
    final endOfWeek = startOfWeek.add(const Duration(days: 6));
    
    return date.isAfter(startOfWeek.subtract(const Duration(days: 1))) &&
           date.isBefore(endOfWeek.add(const Duration(days: 1)));
  }

  /// Get smart date format (Today, Yesterday, or date)
  static String getSmartDateFormat(DateTime date) {
    if (isToday(date)) {
      return 'اليوم ${formatTime(date)}';
    } else if (isYesterday(date)) {
      return 'أمس ${formatTime(date)}';
    } else if (isThisWeek(date)) {
      return '${formatDayName(date)} ${formatTime(date)}';
    } else {
      return formatDateTime(date);
    }
  }

  /// Format duration in Arabic
  static String formatDuration(Duration duration) {
    if (duration.inDays > 0) {
      final days = duration.inDays;
      final hours = duration.inHours % 24;
      if (hours > 0) {
        return '$days ${days == 1 ? 'يوم' : 'أيام'} و $hours ${hours == 1 ? 'ساعة' : 'ساعات'}';
      } else {
        return '$days ${days == 1 ? 'يوم' : 'أيام'}';
      }
    } else if (duration.inHours > 0) {
      final hours = duration.inHours;
      final minutes = duration.inMinutes % 60;
      if (minutes > 0) {
        return '$hours ${hours == 1 ? 'ساعة' : 'ساعات'} و $minutes ${minutes == 1 ? 'دقيقة' : 'دقائق'}';
      } else {
        return '$hours ${hours == 1 ? 'ساعة' : 'ساعات'}';
      }
    } else if (duration.inMinutes > 0) {
      final minutes = duration.inMinutes;
      return '$minutes ${minutes == 1 ? 'دقيقة' : 'دقائق'}';
    } else {
      final seconds = duration.inSeconds;
      return '$seconds ${seconds == 1 ? 'ثانية' : 'ثواني'}';
    }
  }

  /// Get start of day
  static DateTime getStartOfDay(DateTime date) {
    return DateTime(date.year, date.month, date.day);
  }

  /// Get end of day
  static DateTime getEndOfDay(DateTime date) {
    return DateTime(date.year, date.month, date.day, 23, 59, 59, 999);
  }

  /// Get start of week (Monday)
  static DateTime getStartOfWeek(DateTime date) {
    final daysFromMonday = date.weekday - 1;
    return getStartOfDay(date.subtract(Duration(days: daysFromMonday)));
  }

  /// Get end of week (Sunday)
  static DateTime getEndOfWeek(DateTime date) {
    final daysToSunday = 7 - date.weekday;
    return getEndOfDay(date.add(Duration(days: daysToSunday)));
  }

  /// Get start of month
  static DateTime getStartOfMonth(DateTime date) {
    return DateTime(date.year, date.month, 1);
  }

  /// Get end of month
  static DateTime getEndOfMonth(DateTime date) {
    final nextMonth = date.month == 12 
        ? DateTime(date.year + 1, 1, 1)
        : DateTime(date.year, date.month + 1, 1);
    return nextMonth.subtract(const Duration(days: 1));
  }

  /// Parse date string in various formats
  static DateTime? parseDate(String dateString) {
    try {
      // Try different date formats
      final formats = [
        'dd/MM/yyyy',
        'yyyy-MM-dd',
        'dd-MM-yyyy',
        'MM/dd/yyyy',
        'yyyy/MM/dd',
      ];

      for (final format in formats) {
        try {
          return DateFormat(format).parse(dateString);
        } catch (e) {
          continue;
        }
      }

      // Try parsing as ISO string
      return DateTime.parse(dateString);
    } catch (e) {
      return null;
    }
  }

  /// Get age from birth date
  static int getAge(DateTime birthDate) {
    final now = DateTime.now();
    int age = now.year - birthDate.year;
    
    if (now.month < birthDate.month || 
        (now.month == birthDate.month && now.day < birthDate.day)) {
      age--;
    }
    
    return age;
  }

  /// Check if date is in the future
  static bool isFuture(DateTime date) {
    return date.isAfter(DateTime.now());
  }

  /// Check if date is in the past
  static bool isPast(DateTime date) {
    return date.isBefore(DateTime.now());
  }

  /// Get working days between two dates (excluding weekends)
  static int getWorkingDaysBetween(DateTime start, DateTime end) {
    if (start.isAfter(end)) {
      return 0;
    }

    int workingDays = 0;
    DateTime current = start;

    while (current.isBefore(end) || current.isAtSameMomentAs(end)) {
      // In Saudi Arabia, weekend is Friday and Saturday
      if (current.weekday != DateTime.friday && current.weekday != DateTime.saturday) {
        workingDays++;
      }
      current = current.add(const Duration(days: 1));
    }

    return workingDays;
  }
}
