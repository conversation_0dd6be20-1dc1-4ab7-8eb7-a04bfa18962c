import 'package:flutter/material.dart';
import 'package:myrunway/core/models/order_model.dart';

class StatusUtils {
  static Color getStatusColor(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return Color(0xFFFF6B35);
      case OrderStatus.processing:
        return Color(0xFF4A90E2);
      case OrderStatus.shipped:
        return Color(0xFF9B59B6);
      case OrderStatus.delivered:
        return Color(0xFF27AE60);
      case OrderStatus.cancelled:
        return Color(0xFFE74C3C);
    }
  }

  static Color getHandlingStatusColor(HandlingStatus status) {
    switch (status) {
      case HandlingStatus.assigned:
        return Color(0xFF1ABC9C);
      case HandlingStatus.inProgress:
        return Color(0xFFF39C12);
      case HandlingStatus.completed:
        return Color(0xFF2ECC71);
      case HandlingStatus.onHold:
        return Color(0xFF95A5A6);
    }
  }

  static String getStatusText(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return 'في الانتظار';
      case OrderStatus.processing:
        return 'قيد المعالجة';
      case OrderStatus.shipped:
        return 'تم الشحن';
      case OrderStatus.delivered:
        return 'تم التسليم';
      case OrderStatus.cancelled:
        return 'ملغي';
    }
  }

  static String getHandlingStatusText(HandlingStatus status) {
    switch (status) {
      case HandlingStatus.assigned:
        return 'تم التعيين';
      case HandlingStatus.inProgress:
        return 'قيد التنفيذ';
      case HandlingStatus.completed:
        return 'مكتمل';
      case HandlingStatus.onHold:
        return 'معلق';
    }
  }
}
