class FormValidators {
  /// Validate email address
  static String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'يرجى إدخال البريد الإلكتروني';
    }

    final emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );
    if (!emailRegex.hasMatch(value)) {
      return 'يرجى إدخال بريد إلكتروني صحيح';
    }

    return null;
  }

  /// Validate password
  static String? validatePassword(String? value, {int minLength = 6}) {
    if (value == null || value.isEmpty) {
      return 'يرجى إدخال كلمة المرور';
    }

    if (value.length < minLength) {
      return 'كلمة المرور يجب أن تكون $minLength أحرف على الأقل';
    }

    return null;
  }

  /// Validate strong password
  static String? validateStrongPassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'يرجى إدخال كلمة المرور';
    }

    if (value.length < 8) {
      return 'كلمة المرور يجب أن تكون 8 أحرف على الأقل';
    }

    if (!RegExp(r'[A-Z]').hasMatch(value)) {
      return 'كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل';
    }

    if (!RegExp(r'[a-z]').hasMatch(value)) {
      return 'كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل';
    }

    if (!RegExp(r'[0-9]').hasMatch(value)) {
      return 'كلمة المرور يجب أن تحتوي على رقم واحد على الأقل';
    }

    if (!RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(value)) {
      return 'كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل';
    }

    return null;
  }

  /// Validate confirm password
  static String? validateConfirmPassword(String? value, String? password) {
    if (value == null || value.isEmpty) {
      return 'يرجى تأكيد كلمة المرور';
    }

    if (value != password) {
      return 'كلمة المرور غير متطابقة';
    }

    return null;
  }

  /// Validate phone number (Saudi format)
  static String? validateSaudiPhone(String? value) {
    if (value == null || value.isEmpty) {
      return 'يرجى إدخال رقم الهاتف';
    }

    // Remove spaces and special characters
    final cleanPhone = value.replaceAll(RegExp(r'[\s\-\(\)]'), '');

    // Saudi phone number patterns
    final patterns = [
      RegExp(r'^(\+966|966|0)?5[0-9]{8}$'), // Mobile numbers
      RegExp(r'^(\+966|966|0)?1[1-9][0-9]{7}$'), // Landline numbers
    ];

    bool isValid = patterns.any((pattern) => pattern.hasMatch(cleanPhone));

    if (!isValid) {
      return 'يرجى إدخال رقم هاتف سعودي صحيح';
    }

    return null;
  }

  /// Validate required field
  static String? validateRequired(String? value, {String? fieldName}) {
    if (value == null || value.trim().isEmpty) {
      return fieldName != null ? 'يرجى إدخال $fieldName' : 'هذا الحقل مطلوب';
    }
    return null;
  }

  /// Validate name (Arabic and English)
  static String? validateName(String? value, {String? fieldName}) {
    if (value == null || value.trim().isEmpty) {
      return fieldName != null ? 'يرجى إدخال $fieldName' : 'يرجى إدخال الاسم';
    }

    if (value.trim().length < 2) {
      return 'الاسم يجب أن يكون حرفين على الأقل';
    }

    // Allow Arabic, English letters, and spaces
    if (!RegExp(r'^[\u0600-\u06FFa-zA-Z\s]+$').hasMatch(value.trim())) {
      return 'الاسم يجب أن يحتوي على أحرف عربية أو إنجليزية فقط';
    }

    return null;
  }

  /// Validate numeric input
  static String? validateNumeric(String? value, {String? fieldName}) {
    if (value == null || value.trim().isEmpty) {
      return fieldName != null ? 'يرجى إدخال $fieldName' : 'هذا الحقل مطلوب';
    }

    if (double.tryParse(value) == null) {
      return 'يرجى إدخال رقم صحيح';
    }

    return null;
  }

  /// Validate positive number
  static String? validatePositiveNumber(String? value, {String? fieldName}) {
    final numericValidation = validateNumeric(value, fieldName: fieldName);
    if (numericValidation != null) return numericValidation;

    final number = double.parse(value!);
    if (number <= 0) {
      return 'يجب أن يكون الرقم أكبر من الصفر';
    }

    return null;
  }

  /// Validate amount/price
  static String? validateAmount(
    String? value, {
    double? minAmount,
    double? maxAmount,
  }) {
    if (value == null || value.trim().isEmpty) {
      return 'يرجى إدخال المبلغ';
    }

    final amount = double.tryParse(value);
    if (amount == null) {
      return 'يرجى إدخال مبلغ صحيح';
    }

    if (amount < 0) {
      return 'المبلغ لا يمكن أن يكون سالباً';
    }

    if (minAmount != null && amount < minAmount) {
      return 'المبلغ يجب أن يكون $minAmount على الأقل';
    }

    if (maxAmount != null && amount > maxAmount) {
      return 'المبلغ يجب أن يكون $maxAmount على الأكثر';
    }

    return null;
  }

  /// Validate address
  static String? validateAddress(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'يرجى إدخال العنوان';
    }

    if (value.trim().length < 10) {
      return 'العنوان يجب أن يكون 10 أحرف على الأقل';
    }

    return null;
  }

  /// Validate ID number (Saudi National ID or Iqama)
  static String? validateSaudiId(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'يرجى إدخال رقم الهوية';
    }

    final cleanId = value.replaceAll(RegExp(r'\s'), '');

    if (cleanId.length != 10) {
      return 'رقم الهوية يجب أن يكون 10 أرقام';
    }

    if (!RegExp(r'^[0-9]+$').hasMatch(cleanId)) {
      return 'رقم الهوية يجب أن يحتوي على أرقام فقط';
    }

    // Validate Saudi ID checksum
    if (!_validateSaudiIdChecksum(cleanId)) {
      return 'رقم الهوية غير صحيح';
    }

    return null;
  }

  /// Validate date string
  static String? validateDate(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'يرجى إدخال التاريخ';
    }

    try {
      DateTime.parse(value);
      return null;
    } catch (e) {
      return 'يرجى إدخال تاريخ صحيح';
    }
  }

  /// Validate URL
  static String? validateUrl(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'يرجى إدخال الرابط';
    }

    final urlRegex = RegExp(
      r'^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$',
    );

    if (!urlRegex.hasMatch(value)) {
      return 'يرجى إدخال رابط صحيح';
    }

    return null;
  }

  /// Validate minimum length
  static String? validateMinLength(
    String? value,
    int minLength, {
    String? fieldName,
  }) {
    if (value == null || value.isEmpty) {
      return fieldName != null ? 'يرجى إدخال $fieldName' : 'هذا الحقل مطلوب';
    }

    if (value.length < minLength) {
      return fieldName != null
          ? '$fieldName يجب أن يكون $minLength أحرف على الأقل'
          : 'يجب أن يكون $minLength أحرف على الأقل';
    }

    return null;
  }

  /// Validate maximum length
  static String? validateMaxLength(
    String? value,
    int maxLength, {
    String? fieldName,
  }) {
    if (value != null && value.length > maxLength) {
      return fieldName != null
          ? '$fieldName يجب أن يكون $maxLength أحرف على الأكثر'
          : 'يجب أن يكون $maxLength أحرف على الأكثر';
    }

    return null;
  }

  /// Validate range
  static String? validateRange(
    String? value,
    double min,
    double max, {
    String? fieldName,
  }) {
    final numericValidation = validateNumeric(value, fieldName: fieldName);
    if (numericValidation != null) return numericValidation;

    final number = double.parse(value!);
    if (number < min || number > max) {
      return fieldName != null
          ? '$fieldName يجب أن يكون بين $min و $max'
          : 'القيمة يجب أن تكون بين $min و $max';
    }

    return null;
  }

  /// Private helper method to validate Saudi ID checksum
  static bool _validateSaudiIdChecksum(String id) {
    if (id.length != 10) return false;

    int sum = 0;
    for (int i = 0; i < 9; i++) {
      int digit = int.parse(id[i]);
      if (i % 2 == 0) {
        digit *= 2;
        if (digit > 9) {
          digit = digit ~/ 10 + digit % 10;
        }
      }
      sum += digit;
    }

    int checkDigit = (10 - (sum % 10)) % 10;
    return checkDigit == int.parse(id[9]);
  }

  /// Validate phone number (general format)
  static String? phone(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'يرجى إدخال رقم الهاتف';
    }

    // Remove spaces and special characters
    final cleanPhone = value.replaceAll(RegExp(r'[\s\-\(\)]'), '');

    if (cleanPhone.length < 10) {
      return 'رقم الهاتف يجب أن يكون 10 أرقام على الأقل';
    }

    if (!RegExp(r'^[0-9+]+$').hasMatch(cleanPhone)) {
      return 'رقم الهاتف يجب أن يحتوي على أرقام فقط';
    }

    return null;
  }

  /// Validate required field (short name)
  static String? required(String? value) {
    return validateRequired(value);
  }

  /// Validate color code (hex format)
  static String? colorCode(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'يرجى إدخال رمز اللون';
    }

    final colorRegex = RegExp(r'^#[0-9A-Fa-f]{6}$');
    if (!colorRegex.hasMatch(value)) {
      return 'يرجى إدخال رمز لون صحيح (مثال: #FF5722)';
    }

    return null;
  }

  /// Combine multiple validators
  static String? Function(String?) combineValidators(
    List<String? Function(String?)> validators,
  ) {
    return (String? value) {
      for (final validator in validators) {
        final result = validator(value);
        if (result != null) return result;
      }
      return null;
    };
  }
}
