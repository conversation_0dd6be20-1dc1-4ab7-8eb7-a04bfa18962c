import 'dart:math';

class LocationUtil {
  /// Calculate distance between two coordinates using Haversine formula
  /// Returns distance in kilometers
  static double calculateDistance(
    double lat1,
    double lon1,
    double lat2,
    double lon2,
  ) {
    const double earthRadius = 6371; // Earth's radius in kilometers

    double dLat = _degreesToRadians(lat2 - lat1);
    double dLon = _degreesToRadians(lon2 - lon1);

    double a = sin(dLat / 2) * sin(dLat / 2) +
        cos(_degreesToRadians(lat1)) *
            cos(_degreesToRadians(lat2)) *
            sin(dLon / 2) *
            sin(dLon / 2);

    double c = 2 * atan2(sqrt(a), sqrt(1 - a));
    double distance = earthRadius * c;

    return distance;
  }

  /// Convert degrees to radians
  static double _degreesToRadians(double degrees) {
    return degrees * (pi / 180);
  }

  /// Format distance for display
  static String formatDistance(double distanceKm) {
    if (distanceKm < 1) {
      return '${(distanceKm * 1000).round()} متر';
    } else if (distanceKm < 10) {
      return '${distanceKm.toStringAsFixed(1)} كم';
    } else {
      return '${distanceKm.round()} كم';
    }
  }

  /// Check if coordinates are valid
  static bool isValidCoordinate(double? latitude, double? longitude) {
    if (latitude == null || longitude == null) return false;
    
    return latitude >= -90 &&
        latitude <= 90 &&
        longitude >= -180 &&
        longitude <= 180;
  }

  /// Check if location is within Saudi Arabia bounds (approximate)
  static bool isWithinSaudiArabia(double latitude, double longitude) {
    // Approximate bounds for Saudi Arabia
    const double minLat = 16.0;
    const double maxLat = 32.5;
    const double minLon = 34.5;
    const double maxLon = 55.7;

    return latitude >= minLat &&
        latitude <= maxLat &&
        longitude >= minLon &&
        longitude <= maxLon;
  }

  /// Get major Saudi cities coordinates
  static Map<String, Map<String, double>> getSaudiCities() {
    return {
      'الرياض': {'lat': 24.7136, 'lon': 46.6753},
      'جدة': {'lat': 21.4858, 'lon': 39.1925},
      'مكة المكرمة': {'lat': 21.3891, 'lon': 39.8579},
      'المدينة المنورة': {'lat': 24.5247, 'lon': 39.5692},
      'الدمام': {'lat': 26.4207, 'lon': 50.0888},
      'الخبر': {'lat': 26.2172, 'lon': 50.1971},
      'تبوك': {'lat': 28.3998, 'lon': 36.5700},
      'بريدة': {'lat': 26.3260, 'lon': 43.9750},
      'خميس مشيط': {'lat': 18.3000, 'lon': 42.7300},
      'حائل': {'lat': 27.5114, 'lon': 41.6900},
      'الطائف': {'lat': 21.2703, 'lon': 40.4158},
      'الجبيل': {'lat': 27.0174, 'lon': 49.6251},
      'ينبع': {'lat': 24.0896, 'lon': 38.0618},
      'أبها': {'lat': 18.2164, 'lon': 42.5047},
      'نجران': {'lat': 17.4924, 'lon': 44.1277},
      'الأحساء': {'lat': 25.4295, 'lon': 49.5906},
      'القطيف': {'lat': 26.5655, 'lon': 50.0089},
      'عرعر': {'lat': 30.9753, 'lon': 41.0381},
      'سكاكا': {'lat': 29.9697, 'lon': 40.2064},
      'جازان': {'lat': 16.8892, 'lon': 42.5511},
    };
  }

  /// Find nearest city to given coordinates
  static String? findNearestCity(double latitude, double longitude) {
    final cities = getSaudiCities();
    String? nearestCity;
    double minDistance = double.infinity;

    for (final entry in cities.entries) {
      final cityName = entry.key;
      final cityCoords = entry.value;
      
      final distance = calculateDistance(
        latitude,
        longitude,
        cityCoords['lat']!,
        cityCoords['lon']!,
      );

      if (distance < minDistance) {
        minDistance = distance;
        nearestCity = cityName;
      }
    }

    // Only return city if it's within reasonable distance (100km)
    return minDistance <= 100 ? nearestCity : null;
  }

  /// Generate Google Maps URL for navigation
  static String generateGoogleMapsUrl(
    double destinationLat,
    double destinationLon, {
    double? originLat,
    double? originLon,
    String? destinationLabel,
  }) {
    String url = 'https://www.google.com/maps/dir/';
    
    if (originLat != null && originLon != null) {
      url += '$originLat,$originLon/';
    }
    
    url += '$destinationLat,$destinationLon';
    
    if (destinationLabel != null) {
      url += '/@$destinationLat,$destinationLon,15z';
    }
    
    return url;
  }

  /// Generate Apple Maps URL for navigation
  static String generateAppleMapsUrl(
    double destinationLat,
    double destinationLon, {
    double? originLat,
    double? originLon,
    String? destinationLabel,
  }) {
    String url = 'http://maps.apple.com/?';
    
    if (originLat != null && originLon != null) {
      url += 'saddr=$originLat,$originLon&';
    }
    
    url += 'daddr=$destinationLat,$destinationLon';
    
    if (destinationLabel != null) {
      url += '&q=$destinationLabel';
    }
    
    return url;
  }

  /// Parse coordinates from various string formats
  static Map<String, double>? parseCoordinates(String coordinatesString) {
    try {
      // Remove any whitespace and common separators
      String cleaned = coordinatesString
          .replaceAll(RegExp(r'\s+'), '')
          .replaceAll('،', ',')
          .replaceAll('؍', ',');

      // Try different formats
      List<String> formats = [
        r'^(-?\d+\.?\d*),(-?\d+\.?\d*)$', // lat,lon
        r'^(-?\d+\.?\d*)\s*,\s*(-?\d+\.?\d*)$', // lat, lon with spaces
        r'^lat:\s*(-?\d+\.?\d*)\s*,?\s*lon:\s*(-?\d+\.?\d*)$', // lat: x, lon: y
        r'^latitude:\s*(-?\d+\.?\d*)\s*,?\s*longitude:\s*(-?\d+\.?\d*)$', // full names
      ];

      for (String format in formats) {
        RegExp regex = RegExp(format, caseSensitive: false);
        Match? match = regex.firstMatch(cleaned);
        
        if (match != null) {
          double lat = double.parse(match.group(1)!);
          double lon = double.parse(match.group(2)!);
          
          if (isValidCoordinate(lat, lon)) {
            return {'latitude': lat, 'longitude': lon};
          }
        }
      }
      
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Format coordinates for display
  static String formatCoordinates(double latitude, double longitude, {int precision = 6}) {
    return '${latitude.toStringAsFixed(precision)}, ${longitude.toStringAsFixed(precision)}';
  }

  /// Calculate estimated travel time based on distance and average speed
  static Duration estimateTravelTime(double distanceKm, {double averageSpeedKmh = 50}) {
    double hours = distanceKm / averageSpeedKmh;
    return Duration(minutes: (hours * 60).round());
  }

  /// Format travel time for display
  static String formatTravelTime(Duration duration) {
    if (duration.inHours > 0) {
      int hours = duration.inHours;
      int minutes = duration.inMinutes % 60;
      if (minutes > 0) {
        return '$hours ساعة و $minutes دقيقة';
      } else {
        return '$hours ${hours == 1 ? 'ساعة' : 'ساعات'}';
      }
    } else {
      int minutes = duration.inMinutes;
      return '$minutes ${minutes == 1 ? 'دقيقة' : 'دقائق'}';
    }
  }

  /// Check if delivery is within service area
  static bool isWithinServiceArea(
    double deliveryLat,
    double deliveryLon,
    double officeLat,
    double officeLon,
    double maxDistanceKm,
  ) {
    double distance = calculateDistance(deliveryLat, deliveryLon, officeLat, officeLon);
    return distance <= maxDistanceKm;
  }

  /// Get delivery zones for a city
  static List<String> getDeliveryZones(String cityName) {
    // This would typically come from a database or API
    // For now, return some common zones for major cities
    switch (cityName) {
      case 'الرياض':
        return [
          'شمال الرياض',
          'جنوب الرياض',
          'شرق الرياض',
          'غرب الرياض',
          'وسط الرياض',
          'حي النخيل',
          'حي الملز',
          'حي العليا',
          'حي الورود',
          'حي السليمانية',
        ];
      case 'جدة':
        return [
          'شمال جدة',
          'جنوب جدة',
          'وسط جدة',
          'حي الروضة',
          'حي الزهراء',
          'حي النزهة',
          'حي الصفا',
          'حي المرجان',
        ];
      case 'الدمام':
        return [
          'وسط الدمام',
          'شرق الدمام',
          'غرب الدمام',
          'حي الفيصلية',
          'حي الشاطئ',
          'حي الجلوية',
        ];
      default:
        return ['المنطقة الوسطى', 'المنطقة الشمالية', 'المنطقة الجنوبية'];
    }
  }
}
