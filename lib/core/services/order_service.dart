import 'package:get/get.dart';
import 'package:myrunway/core/models/order_model_new.dart';
import 'package:myrunway/core/models/order_history_models.dart';
import 'package:myrunway/core/constants/api_endpoints.dart';
import 'package:myrunway/core/services/api_service.dart';

class OrderService extends GetxService {
  final ApiService _apiService = Get.find<ApiService>();

  // Get all orders with optional filters
  Future<ApiResponse<List<OrderModelNew>>> getOrders({
    String? dateFrom,
    String? dateTo,
    String? status,
    int? assignedTo,
  }) async {
    final Map<String, dynamic> query = {};
    if (dateFrom != null) query['date_from'] = dateFrom;
    if (dateTo != null) query['date_to'] = dateTo;
    if (status != null) query['status'] = status;
    if (assignedTo != null) query['assigned_to'] = assignedTo;

    final response = await _apiService.get<List<dynamic>>(
      ApiEndpoints.orders,
      (data) => data as List<dynamic>,
      query: query.isNotEmpty ? query : null,
    );

    if (response.success && response.data != null) {
      final orders =
          response.data!
              .map(
                (json) => OrderModelNew.fromJson(json as Map<String, dynamic>),
              )
              .toList();
      return ApiResponse.success(orders);
    }

    return ApiResponse.error(
      response.message ?? 'فشل في جلب قائمة الطلبات',
      statusCode: response.statusCode,
    );
  }

  // Get current user's orders
  Future<ApiResponse<List<OrderModelNew>>> getMyOrders({
    String? dateFrom,
    String? dateTo,
    String? status,
  }) async {
    final Map<String, dynamic> query = {};
    if (dateFrom != null) query['date_from'] = dateFrom;
    if (dateTo != null) query['date_to'] = dateTo;
    if (status != null) query['status'] = status;

    final response = await _apiService.get<List<dynamic>>(
      '${ApiEndpoints.orders}/me/',
      (data) => data as List<dynamic>,
      query: query.isNotEmpty ? query : null,
    );

    if (response.success && response.data != null) {
      final orders =
          response.data!
              .map(
                (json) => OrderModelNew.fromJson(json as Map<String, dynamic>),
              )
              .toList();
      return ApiResponse.success(orders);
    }

    return ApiResponse.error(
      response.message ?? 'فشل في جلب طلباتك',
      statusCode: response.statusCode,
    );
  }

  // Create a new order
  Future<ApiResponse<OrderModelNew>> createOrder(
    OrderCreateRequest request,
  ) async {
    final response = await _apiService.post<Map<String, dynamic>>(
      ApiEndpoints.createOrder,
      (data) => data as Map<String, dynamic>,
      body: request.toJson(),
    );

    if (response.success && response.data != null) {
      final order = OrderModelNew.fromJson(response.data!);
      return ApiResponse.success(order);
    }

    return ApiResponse.error(
      response.message ?? 'فشل في إنشاء الطلب',
      statusCode: response.statusCode,
      errors: response.errors,
    );
  }

  // Update an existing order
  Future<ApiResponse<OrderModelNew>> updateOrder(
    int orderId,
    OrderCreateRequest request,
  ) async {
    final response = await _apiService.put<Map<String, dynamic>>(
      ApiEndpoints.orderById(orderId.toString()),
      (data) => data as Map<String, dynamic>,
      body: request.toJson(),
    );

    if (response.success && response.data != null) {
      final order = OrderModelNew.fromJson(response.data!);
      return ApiResponse.success(order);
    }

    return ApiResponse.error(
      response.message ?? 'فشل في تحديث الطلب',
      statusCode: response.statusCode,
      errors: response.errors,
    );
  }

  // Delete an order
  Future<ApiResponse<bool>> deleteOrder(int orderId) async {
    final response = await _apiService.delete<bool>(
      ApiEndpoints.orderById(orderId.toString()),
      (data) => data as bool,
    );

    if (response.success) {
      return ApiResponse.success(true);
    }

    return ApiResponse.error(
      response.message ?? 'فشل في حذف الطلب',
      statusCode: response.statusCode,
    );
  }

  // Get unassigned orders (status = PENDING)
  Future<ApiResponse<List<OrderModelNew>>> getUnassignedOrders() async {
    final response = await _apiService.get<List<dynamic>>(
      ApiEndpoints.orders,
      (data) => data as List<dynamic>,
      query: {'status': 'PENDING'},
    );

    if (response.success && response.data != null) {
      final orders =
          response.data!
              .map(
                (json) => OrderModelNew.fromJson(json as Map<String, dynamic>),
              )
              .toList();
      return ApiResponse.success(orders);
    }

    return ApiResponse.error(
      response.message ?? 'فشل في جلب الطلبات غير المُعيَّنة',
      statusCode: response.statusCode,
    );
  }

  // Assign order to employee
  Future<ApiResponse<OrderModelNew>> assignOrder(
    int orderId,
    OrderAssignRequest request,
  ) async {
    final response = await _apiService.post<Map<String, dynamic>>(
      ApiEndpoints.assignOrderToEmployee(orderId.toString()),
      (data) => data as Map<String, dynamic>,
      body: request.toJson(),
    );

    if (response.success && response.data != null) {
      final order = OrderModelNew.fromJson(response.data!);
      return ApiResponse.success(order);
    }

    return ApiResponse.error(
      response.message ?? 'فشل في تعيين الطلب',
      statusCode: response.statusCode,
      errors: response.errors,
    );
  }

  // Assign multiple orders to employee
  Future<ApiResponse<List<OrderModelNew>>> assignMultipleOrders(
    List<int> orderIds,
    OrderAssignRequest request,
  ) async {
    final List<OrderModelNew> assignedOrders = [];
    final List<String> errors = [];

    for (final orderId in orderIds) {
      final response = await assignOrder(orderId, request);
      if (response.success && response.data != null) {
        assignedOrders.add(response.data!);
      } else {
        errors.add('فشل في تعيين الطلب #$orderId: ${response.message}');
      }
    }

    if (errors.isEmpty) {
      return ApiResponse.success(assignedOrders);
    } else if (assignedOrders.isNotEmpty) {
      return ApiResponse.success(
        assignedOrders,
        message:
            'تم تعيين ${assignedOrders.length} طلب بنجاح، فشل في ${errors.length} طلب',
      );
    } else {
      return ApiResponse.error(
        'فشل في تعيين جميع الطلبات: ${errors.join(', ')}',
      );
    }
  }

  // Complete an order
  Future<ApiResponse<OrderModelNew>> completeOrder(
    int orderId,
    OrderCompleteRequest request,
  ) async {
    final response = await _apiService.post<Map<String, dynamic>>(
      ApiEndpoints.completeOrder(orderId.toString()),
      (data) => data as Map<String, dynamic>,
      body: request.toJson(),
    );

    if (response.success && response.data != null) {
      final order = OrderModelNew.fromJson(response.data!);
      return ApiResponse.success(order);
    }

    return ApiResponse.error(
      response.message ?? 'فشل في إكمال الطلب',
      statusCode: response.statusCode,
      errors: response.errors,
    );
  }

  // Get order by ID
  Future<ApiResponse<OrderModelNew>> getOrderById(int orderId) async {
    final response = await _apiService.get<Map<String, dynamic>>(
      ApiEndpoints.orderById(orderId.toString()),
      (data) => data as Map<String, dynamic>,
    );

    if (response.success && response.data != null) {
      final order = OrderModelNew.fromJson(response.data!);
      return ApiResponse.success(order);
    }

    return ApiResponse.error(
      response.message ?? 'فشل في جلب بيانات الطلب',
      statusCode: response.statusCode,
    );
  }

  // Get order with history by ID
  Future<ApiResponse<OrderWithHistory>> getOrderWithHistory(int orderId) async {
    final response = await _apiService.get<Map<String, dynamic>>(
      ApiEndpoints.orderById(orderId.toString()),
      (data) => data as Map<String, dynamic>,
    );

    if (response.success && response.data != null) {
      final orderWithHistory = OrderWithHistory.fromJson(response.data!);
      return ApiResponse.success(orderWithHistory);
    }

    return ApiResponse.error(
      response.message ?? 'فشل في جلب تفاصيل الطلب والتاريخ',
      statusCode: response.statusCode,
    );
  }

  // Transfer order to another employee
  Future<ApiResponse<OrderModelNew>> transferOrder(
    int orderId,
    OrderTransferRequest request,
  ) async {
    final response = await _apiService.post<Map<String, dynamic>>(
      ApiEndpoints.transferOrder(orderId.toString()),
      (data) => data as Map<String, dynamic>,
      body: request.toJson(),
    );

    if (response.success && response.data != null) {
      final order = OrderModelNew.fromJson(response.data!);
      return ApiResponse.success(order);
    }

    return ApiResponse.error(
      response.message ?? 'فشل في نقل الطلب',
      statusCode: response.statusCode,
      errors: response.errors,
    );
  }

  // Update order status
  Future<ApiResponse<OrderModelNew>> updateOrderStatus(
    int orderId,
    OrderStatusUpdateRequest request,
  ) async {
    final response = await _apiService.put<Map<String, dynamic>>(
      ApiEndpoints.orderById(orderId.toString()),
      (data) => data as Map<String, dynamic>,
      body: request.toJson(),
    );

    if (response.success && response.data != null) {
      final order = OrderModelNew.fromJson(response.data!);
      return ApiResponse.success(order);
    }

    return ApiResponse.error(
      response.message ?? 'فشل في تحديث حالة الطلب',
      statusCode: response.statusCode,
      errors: response.errors,
    );
  }
}
