import 'package:get/get.dart';
import 'package:myrunway/core/models/company_model.dart';
import 'package:myrunway/core/constants/api_endpoints.dart';
import 'package:myrunway/core/services/api_service.dart';

class CompanyService extends GetxService {
  final ApiService _apiService = Get.find<ApiService>();

  // Get all companies
  Future<ApiResponse<List<CompanyModel>>> getCompanies() async {
    final response = await _apiService.get<List<dynamic>>(
      ApiEndpoints.companies,
      (data) => data as List<dynamic>,
    );

    if (response.success && response.data != null) {
      final companies = response.data!
          .map((json) => CompanyModel.fromJson(json as Map<String, dynamic>))
          .toList();
      return ApiResponse.success(companies);
    }

    return ApiResponse.error(
      response.message ?? 'فشل في جلب قائمة الشركات',
      statusCode: response.statusCode,
    );
  }

  // Create a new company
  Future<ApiResponse<CompanyModel>> createCompany(
    CompanyCreateRequest request,
  ) async {
    final response = await _apiService.post<Map<String, dynamic>>(
      ApiEndpoints.createCompany,
      (data) => data as Map<String, dynamic>,
      body: request.toJson(),
    );

    if (response.success && response.data != null) {
      final company = CompanyModel.fromJson(response.data!);
      return ApiResponse.success(company);
    }

    return ApiResponse.error(
      response.message ?? 'فشل في إنشاء الشركة',
      statusCode: response.statusCode,
      errors: response.errors,
    );
  }

  // Update an existing company
  Future<ApiResponse<CompanyModel>> updateCompany(
    int companyId,
    CompanyEditRequest request,
  ) async {
    if (request.isEmpty) {
      return ApiResponse.error('لا توجد بيانات للتحديث');
    }

    final response = await _apiService.put<Map<String, dynamic>>(
      ApiEndpoints.companyById(companyId.toString()),
      (data) => data as Map<String, dynamic>,
      body: request.toJson(),
    );

    if (response.success && response.data != null) {
      final company = CompanyModel.fromJson(response.data!);
      return ApiResponse.success(company);
    }

    return ApiResponse.error(
      response.message ?? 'فشل في تحديث الشركة',
      statusCode: response.statusCode,
      errors: response.errors,
    );
  }

  // Delete a company
  Future<ApiResponse<bool>> deleteCompany(int companyId) async {
    final response = await _apiService.delete<Map<String, dynamic>>(
      ApiEndpoints.companyById(companyId.toString()),
      (data) => data as Map<String, dynamic>,
    );

    if (response.success) {
      return ApiResponse.success(true);
    }

    return ApiResponse.error(
      response.message ?? 'فشل في حذف الشركة',
      statusCode: response.statusCode,
    );
  }

  // Get company by ID
  Future<ApiResponse<CompanyModel>> getCompanyById(int companyId) async {
    final response = await _apiService.get<Map<String, dynamic>>(
      ApiEndpoints.companyById(companyId.toString()),
      (data) => data as Map<String, dynamic>,
    );

    if (response.success && response.data != null) {
      final company = CompanyModel.fromJson(response.data!);
      return ApiResponse.success(company);
    }

    return ApiResponse.error(
      response.message ?? 'فشل في جلب بيانات الشركة',
      statusCode: response.statusCode,
    );
  }
}
