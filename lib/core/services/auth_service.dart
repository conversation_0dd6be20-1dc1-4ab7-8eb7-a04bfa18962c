import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:logging/logging.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:myrunway/core/models/user_model.dart';
import 'package:myrunway/core/constants/user_roles.dart';
import 'package:myrunway/core/constants/api_endpoints.dart';
import 'package:myrunway/core/services/api_service.dart';

final _logger = Logger('AuthService');

class AuthService extends GetxService {
  static AuthService get to => Get.find<AuthService>();

  final ApiService _apiService = Get.find<ApiService>();

  final Rx<UserModel?> _currentUser = Rx<UserModel?>(null);
  final RxBool _isLoggedIn = false.obs;
  final RxBool _isLoading = false.obs;

  // Getters
  UserModel? get currentUser {
    _logger.finest(
      'Getting current user: ${_currentUser.value?.fullName ?? 'null'}',
    );
    return _currentUser.value;
  }

  bool get isLoggedIn {
    _logger.finest('Getting login status: ${_isLoggedIn.value}');
    return _isLoggedIn.value;
  }

  bool get isLoading {
    _logger.finest('Getting loading status: ${_isLoading.value}');
    return _isLoading.value;
  }

  UserRole? get currentUserRole {
    final role = _currentUser.value?.role;
    _logger.finest('Getting current user role: $role');
    return role;
  }

  Future<AuthService> init() async {
    _logger.info('AuthService initializing');
    await checkAuthStatus();
    _logger.info('AuthService initialization completed');
    return this;
  }

  Future<void> checkAuthStatus() async {
    _logger.info('Starting authentication status check');

    _logger.fine('Retrieving SharedPreferences instance');
    final prefs = await SharedPreferences.getInstance();

    final token = prefs.getString('auth_token');
    _logger.info(
      'Retrieved stored token: ${token != null ? '[TOKEN_EXISTS]' : '[NO_TOKEN]'}',
    );

    if (token != null && token.isNotEmpty) {
      _logger.info('Valid token found, setting auth token in API service');
      _apiService.setAuthToken(token);

      _logger.info('Fetching current user information from API');

      final userResponse = await _apiService.get<Map<String, dynamic>>(
        ApiEndpoints.getCurrentUser,
        (data) => data as Map<String, dynamic>,
      );

      _logger.fine('API response received - success: ${userResponse.success}');

      if (userResponse.success && userResponse.data != null) {
        _logger.info('Successfully retrieved user data from API');

        _currentUser.value = UserModel.fromJson(userResponse.data!);
        _isLoggedIn.value = true;

        _logger.info(
          'User authentication successful - User: ${_currentUser.value!.fullName}, Role: ${_currentUser.value!.role}',
        );
      } else {
        _logger.warning(
          'Invalid API response or token expired - clearing stored token',
        );
        _logger.fine('Response message: ${userResponse.message}');
        await _clearStoredAuthToken();
        _isLoggedIn.value = false;
      }
    } else {
      _logger.info('No stored token found - user not authenticated');
      _isLoggedIn.value = false;
    }

    _logger.info(
      'Authentication status check completed - isLoggedIn: ${_isLoggedIn.value}',
    );
  }

  Future<bool> login(String username, String password, int officeId) async {
    _logger.info('Login attempt started');
    _logger.info('Login parameters - username: $username, officeId: $officeId');

    _isLoading.value = true;
    _logger.fine('Loading state set to true');

    _logger.info('Making login API request');

    final response = await _apiService.post<Map<String, dynamic>>(
      ApiEndpoints.login,
      (data) => data as Map<String, dynamic>,
      query: {'phone': username, 'password': password, 'office_id': officeId},
    );

    _logger.info('Login API response received - success: ${response.success}');
    _logger.fine('Response message: ${response.message}');

    if (response.success && response.data != null) {
      final loginData = response.data!;
      _logger.info('Login API call successful, processing response data');
      _logger.fine('Response data keys: ${loginData.keys.toList()}');

      if (loginData['token'] != null) {
        _logger.info('Token received from API, setting auth token');
        _apiService.setAuthToken(loginData['token']);

        await _storeAuthToken(loginData['token']);
        _logger.info('Auth token stored successfully');
      } else {
        _logger.warning('No token received in login response');
      }

      _logger.info('Parsing user data from login response');
      _currentUser.value = UserModel.fromJson(loginData['user']);
      _isLoggedIn.value = true;

      _logger.info(
        'Login successful - User: ${_currentUser.value!.fullName}, Role: ${_currentUser.value!.role}, ID: ${_currentUser.value!.id}',
      );

      _logger.fine('Setting loading state to false');
      _isLoading.value = false;
      _logger.info('Login attempt completed');
      return true;
    } else {
      _logger.warning('Login failed - API returned unsuccessful response');
      _logger.fine('Failure reason: ${response.message}');

      _logger.fine('Setting loading state to false');
      _isLoading.value = false;
      _logger.info('Login attempt completed');
      return false;
    }
  }

  Future<void> _storeAuthToken(String token) async {
    _logger.fine('Storing auth token to SharedPreferences');

    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('auth_token', token);
    _logger.fine('Auth token stored successfully');
  }

  Future<void> _clearStoredAuthToken() async {
    _logger.fine('Clearing stored auth token from SharedPreferences');

    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('auth_token');
    _logger.fine('Auth token cleared successfully');
  }

  Future<void> logout() async {
    _logger.info('Logout process started');

    _isLoading.value = true;
    _logger.fine('Loading state set to true');

    _logger.info(
      'Logout API call not implemented - proceeding with local cleanup',
    );
    // No logout endpoint in the current API, just clear local data
    // If logout endpoint is added later, uncomment below:
    // _logger.info('Making logout API request');
    // await _apiService.post<Map<String, dynamic>>(
    //   ApiEndpoints.logout,
    //   (data) => data as Map<String, dynamic>,
    // );
    // _logger.info('Logout API call completed');

    _logger.info('Clearing local authentication data');

    // Clear local data
    final previousUser = _currentUser.value?.fullName;
    _currentUser.value = null;
    _isLoggedIn.value = false;
    _apiService.clearAuthToken();

    _logger.info('Cleared user data for: ${previousUser ?? 'unknown user'}');

    // Clear stored token
    await _clearStoredAuthToken();
    _logger.info('Local authentication data cleared successfully');

    _isLoading.value = false;
    _logger.fine('Loading state set to false');

    _logger.info('Showing logout success message');
    Get.snackbar(
      'تم تسجيل الخروج',
      'تم تسجيل الخروج بنجاح',
      backgroundColor: Colors.blue,
      colorText: Colors.white,
    );

    _logger.info('Logout process completed');
  }

  Widget getHomePageForCurrentUser() {
    _logger.fine('Getting home page for current user');

    if (_currentUser.value == null) {
      _logger.warning('No current user - returning empty container');
      // Return login page or splash screen
      return Container(); // This will be replaced with actual login page
    }

    _logger.info(
      'Determining home page for user role: ${_currentUser.value!.role}',
    );

    switch (_currentUser.value!.role) {
      case UserRole.employee:
        _logger.fine('Returning employee home page');
        // Return employee home page
        return Container(); // This will be replaced with actual employee home page
      case UserRole.manager:
        _logger.fine('Returning manager home page');
        // Return manager home page
        return Container(); // This will be replaced with actual manager home page
      case UserRole.master:
        _logger.fine('Returning master home page');
        // Return master home page
        return Container(); // This will be replaced with actual master home page
    }
  }

  bool hasPermission(UserRole requiredRole) {
    _logger.fine('Checking permission for role: $requiredRole');

    if (_currentUser.value == null) {
      _logger.warning('Permission check failed - no current user');
      return false;
    }

    final hasAccess = _currentUser.value!.canAccess(requiredRole);
    _logger.fine('Permission check result for $requiredRole: $hasAccess');

    return hasAccess;
  }

  bool canAccessEmployeeManagement() {
    _logger.fine('Checking employee management access');
    return hasPermission(UserRole.manager);
  }

  bool canAccessOrderManagement() {
    _logger.fine('Checking order management access');
    return hasPermission(UserRole.manager);
  }

  bool canAccessClientManagement() {
    _logger.fine('Checking client management access');
    return hasPermission(UserRole.manager);
  }

  bool canAccessOfficeManagement() {
    _logger.fine('Checking office management access');
    return hasPermission(UserRole.master);
  }

  bool canAccessReports() {
    _logger.fine('Checking reports access');
    return hasPermission(UserRole.manager);
  }

  bool canAccessAdvancedReports() {
    _logger.fine('Checking advanced reports access');
    return hasPermission(UserRole.master);
  }

  bool canAccessSystemSettings() {
    _logger.fine('Checking system settings access');
    return hasPermission(UserRole.master);
  }

  Future<bool> updateProfile(Map<String, dynamic> profileData) async {
    _logger.info('Profile update started');
    _logger.fine('Profile data keys: ${profileData.keys.toList()}');

    _isLoading.value = true;
    _logger.fine('Loading state set to true');

    _logger.info('Making update profile API request');

    final response = await _apiService.put<Map<String, dynamic>>(
      ApiEndpoints.updateProfile,
      (data) => data as Map<String, dynamic>,
      body: profileData,
    );

    _logger.info(
      'Update profile API response received - success: ${response.success}',
    );

    if (response.success && response.data != null) {
      _logger.info('Profile update successful, updating local user data');

      _currentUser.value = UserModel.fromJson(response.data!);
      _logger.info('Local user data updated successfully');

      Get.snackbar(
        'تم التحديث',
        'تم تحديث الملف الشخصي بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );

      _logger.fine('Setting loading state to false');
      _isLoading.value = false;
      _logger.info('Profile update process completed');
      return true;
    } else {
      _logger.warning('Profile update failed');
      _logger.fine('Failure reason: ${response.message}');

      Get.snackbar(
        'خطأ',
        response.message ?? 'فشل في تحديث الملف الشخصي',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );

      _logger.fine('Setting loading state to false');
      _isLoading.value = false;
      _logger.info('Profile update process completed');
      return false;
    }
  }

  Future<bool> changePassword(
    String currentPassword,
    String newPassword,
  ) async {
    _logger.info('Password change started');
    _logger.fine(
      'Password change request for user: ${_currentUser.value?.fullName}',
    );

    _isLoading.value = true;
    _logger.fine('Loading state set to true');

    _logger.info('Making change password API request');

    final response = await _apiService.post<Map<String, dynamic>>(
      ApiEndpoints.changePassword,
      (data) => data as Map<String, dynamic>,
      body: {'current_password': currentPassword, 'new_password': newPassword},
    );

    _logger.info(
      'Change password API response received - success: ${response.success}',
    );

    if (response.success) {
      _logger.info('Password change successful');

      Get.snackbar(
        'تم التحديث',
        'تم تغيير كلمة المرور بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );

      _logger.fine('Setting loading state to false');
      _isLoading.value = false;
      _logger.info('Password change process completed');
      return true;
    } else {
      _logger.warning('Password change failed');
      _logger.fine('Failure reason: ${response.message}');

      Get.snackbar(
        'خطأ',
        response.message ?? 'فشل في تغيير كلمة المرور',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );

      _logger.fine('Setting loading state to false');
      _isLoading.value = false;
      _logger.info('Password change process completed');
      return false;
    }
  }
}
