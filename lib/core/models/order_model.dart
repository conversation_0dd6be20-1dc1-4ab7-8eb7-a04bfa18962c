import 'package:flutter/material.dart';
import 'package:myrunway/core/constants/app_colors.dart';

enum OrderStatus { pending, processing, shipped, delivered, cancelled }

enum HandlingStatus { assigned, inProgress, completed, onHold }

extension OrderStatusExtension on OrderStatus {
  String get name {
    switch (this) {
      case OrderStatus.pending:
        return 'pending';
      case OrderStatus.processing:
        return 'processing';
      case OrderStatus.shipped:
        return 'shipped';
      case OrderStatus.delivered:
        return 'delivered';
      case OrderStatus.cancelled:
        return 'cancelled';
    }
  }

  String get displayName {
    switch (this) {
      case OrderStatus.pending:
        return 'في الانتظار';
      case OrderStatus.processing:
        return 'قيد المعالجة';
      case OrderStatus.shipped:
        return 'تم الشحن';
      case OrderStatus.delivered:
        return 'تم التسليم';
      case OrderStatus.cancelled:
        return 'ملغي';
    }
  }

  Color get color {
    switch (this) {
      case OrderStatus.pending:
        return AppColors.pending;
      case OrderStatus.processing:
        return AppColors.processing;
      case OrderStatus.shipped:
        return AppColors.shipped;
      case OrderStatus.delivered:
        return AppColors.delivered;
      case OrderStatus.cancelled:
        return AppColors.cancelled;
    }
  }

  IconData get icon {
    switch (this) {
      case OrderStatus.pending:
        return Icons.schedule;
      case OrderStatus.processing:
        return Icons.autorenew;
      case OrderStatus.shipped:
        return Icons.local_shipping;
      case OrderStatus.delivered:
        return Icons.check_circle;
      case OrderStatus.cancelled:
        return Icons.cancel;
    }
  }

  static OrderStatus fromString(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return OrderStatus.pending;
      case 'processing':
        return OrderStatus.processing;
      case 'shipped':
        return OrderStatus.shipped;
      case 'delivered':
        return OrderStatus.delivered;
      case 'cancelled':
        return OrderStatus.cancelled;
      default:
        return OrderStatus.pending;
    }
  }
}

extension HandlingStatusExtension on HandlingStatus {
  String get name {
    switch (this) {
      case HandlingStatus.assigned:
        return 'assigned';
      case HandlingStatus.inProgress:
        return 'inProgress';
      case HandlingStatus.completed:
        return 'completed';
      case HandlingStatus.onHold:
        return 'onHold';
    }
  }

  String get displayName {
    switch (this) {
      case HandlingStatus.assigned:
        return 'مُعيَّن';
      case HandlingStatus.inProgress:
        return 'قيد التنفيذ';
      case HandlingStatus.completed:
        return 'مكتمل';
      case HandlingStatus.onHold:
        return 'معلق';
    }
  }

  Color get color {
    switch (this) {
      case HandlingStatus.assigned:
        return AppColors.assigned;
      case HandlingStatus.inProgress:
        return AppColors.inProgress;
      case HandlingStatus.completed:
        return AppColors.completed;
      case HandlingStatus.onHold:
        return AppColors.onHold;
    }
  }

  static HandlingStatus fromString(String status) {
    switch (status.toLowerCase()) {
      case 'assigned':
        return HandlingStatus.assigned;
      case 'inprogress':
      case 'in_progress':
        return HandlingStatus.inProgress;
      case 'completed':
        return HandlingStatus.completed;
      case 'onhold':
      case 'on_hold':
        return HandlingStatus.onHold;
      default:
        return HandlingStatus.assigned;
    }
  }
}

class OrderModel {
  final String id;
  final String customerName;
  final String customerPhone;
  final double totalAmount;
  final DateTime assignedDate;
  final String assignee;
  final String? assigneeId;
  final OrderStatus status;
  final HandlingStatus handlingStatus;
  final String address;
  final int itemsCount;
  final double collectedAmount;
  final double commissionRate;
  final double commissionAmount;
  final String? clientId;
  final String? clientName;
  final String? officeId;
  final String? officeName;
  final DateTime? deliveredAt;
  final String? notes;
  final List<String>? proofImages;
  final Map<String, dynamic>? metadata;

  OrderModel({
    required this.id,
    required this.customerName,
    required this.customerPhone,
    required this.totalAmount,
    required this.assignedDate,
    required this.assignee,
    this.assigneeId,
    required this.status,
    required this.handlingStatus,
    required this.address,
    required this.itemsCount,
    required this.collectedAmount,
    required this.commissionRate,
    required this.commissionAmount,
    this.clientId,
    this.clientName,
    this.officeId,
    this.officeName,
    this.deliveredAt,
    this.notes,
    this.proofImages,
    this.metadata,
  });

  factory OrderModel.fromJson(Map<String, dynamic> json) {
    return OrderModel(
      id: json['id'] ?? '',
      customerName: json['customer_name'] ?? '',
      customerPhone: json['customer_phone'] ?? '',
      totalAmount: (json['total_amount'] ?? 0.0).toDouble(),
      assignedDate: DateTime.parse(json['assigned_date'] ?? DateTime.now().toIso8601String()),
      assignee: json['assignee'] ?? '',
      assigneeId: json['assignee_id'],
      status: OrderStatusExtension.fromString(json['status'] ?? 'pending'),
      handlingStatus: HandlingStatusExtension.fromString(json['handling_status'] ?? 'assigned'),
      address: json['address'] ?? '',
      itemsCount: json['items_count'] ?? 0,
      collectedAmount: (json['collected_amount'] ?? 0.0).toDouble(),
      commissionRate: (json['commission_rate'] ?? 0.0).toDouble(),
      commissionAmount: (json['commission_amount'] ?? 0.0).toDouble(),
      clientId: json['client_id'],
      clientName: json['client_name'],
      officeId: json['office_id'],
      officeName: json['office_name'],
      deliveredAt: json['delivered_at'] != null ? DateTime.parse(json['delivered_at']) : null,
      notes: json['notes'],
      proofImages: json['proof_images'] != null ? List<String>.from(json['proof_images']) : null,
      metadata: json['metadata'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'customer_name': customerName,
      'customer_phone': customerPhone,
      'total_amount': totalAmount,
      'assigned_date': assignedDate.toIso8601String(),
      'assignee': assignee,
      'assignee_id': assigneeId,
      'status': status.name,
      'handling_status': handlingStatus.name,
      'address': address,
      'items_count': itemsCount,
      'collected_amount': collectedAmount,
      'commission_rate': commissionRate,
      'commission_amount': commissionAmount,
      'client_id': clientId,
      'client_name': clientName,
      'office_id': officeId,
      'office_name': officeName,
      'delivered_at': deliveredAt?.toIso8601String(),
      'notes': notes,
      'proof_images': proofImages,
      'metadata': metadata,
    };
  }

  OrderModel copyWith({
    String? id,
    String? customerName,
    String? customerPhone,
    double? totalAmount,
    DateTime? assignedDate,
    String? assignee,
    String? assigneeId,
    OrderStatus? status,
    HandlingStatus? handlingStatus,
    String? address,
    int? itemsCount,
    double? collectedAmount,
    double? commissionRate,
    double? commissionAmount,
    String? clientId,
    String? clientName,
    String? officeId,
    String? officeName,
    DateTime? deliveredAt,
    String? notes,
    List<String>? proofImages,
    Map<String, dynamic>? metadata,
  }) {
    return OrderModel(
      id: id ?? this.id,
      customerName: customerName ?? this.customerName,
      customerPhone: customerPhone ?? this.customerPhone,
      totalAmount: totalAmount ?? this.totalAmount,
      assignedDate: assignedDate ?? this.assignedDate,
      assignee: assignee ?? this.assignee,
      assigneeId: assigneeId ?? this.assigneeId,
      status: status ?? this.status,
      handlingStatus: handlingStatus ?? this.handlingStatus,
      address: address ?? this.address,
      itemsCount: itemsCount ?? this.itemsCount,
      collectedAmount: collectedAmount ?? this.collectedAmount,
      commissionRate: commissionRate ?? this.commissionRate,
      commissionAmount: commissionAmount ?? this.commissionAmount,
      clientId: clientId ?? this.clientId,
      clientName: clientName ?? this.clientName,
      officeId: officeId ?? this.officeId,
      officeName: officeName ?? this.officeName,
      deliveredAt: deliveredAt ?? this.deliveredAt,
      notes: notes ?? this.notes,
      proofImages: proofImages ?? this.proofImages,
      metadata: metadata ?? this.metadata,
    );
  }

  // Helper methods
  bool get isDelivered => status == OrderStatus.delivered;
  bool get isPending => status == OrderStatus.pending;
  bool get isCompleted => handlingStatus == HandlingStatus.completed;
  bool get hasPartialCollection => collectedAmount > 0 && collectedAmount < totalAmount;
  bool get hasFullCollection => collectedAmount >= totalAmount;
  double get remainingAmount => totalAmount - collectedAmount;

  @override
  String toString() {
    return 'OrderModel(id: $id, customerName: $customerName, status: ${status.name})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is OrderModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

// For backward compatibility with existing code
typedef Order = OrderModel;
