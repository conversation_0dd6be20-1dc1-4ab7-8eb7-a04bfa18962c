import 'package:myrunway/core/constants/user_roles.dart';
import 'package:myrunway/core/models/office_model.dart';

class UserModel {
  final int? id;
  final String username;
  final String? firstName;
  final String? lastName;
  final String? email;
  final DateTime dateJoined;
  final UserRole role;
  final OfficeModel? office;
  final String? phone;
  final double? commissionRate;
  final double? currentLocationLat;
  final double? currentLocationLng;
  final DateTime? lastLocationUpdate;

  UserModel({
    this.id,
    required this.username,
    this.firstName,
    this.lastName,
    this.email,
    required this.dateJoined,
    required this.role,
    this.office,
    this.phone,
    this.commissionRate,
    this.currentLocationLat,
    this.currentLocationLng,
    this.lastLocationUpdate,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'],
      username: json['username'] ?? '',
      firstName: json['first_name'],
      lastName: json['last_name'],
      email: json['email'],
      dateJoined: DateTime.parse(
        json['date_joined'] ?? DateTime.now().toIso8601String(),
      ),
      role: UserRoleExtension.fromString(json['role'] ?? 'EMPLOYEE'),
      office:
          json['office'] != null ? OfficeModel.fromJson(json['office']) : null,
      phone: json['phone'],
      commissionRate:
          json['commission_rate'] != null
              ? double.tryParse(json['commission_rate'].toString())
              : null,
      currentLocationLat:
          json['current_location_lat'] != null
              ? double.tryParse(json['current_location_lat'].toString())
              : null,
      currentLocationLng:
          json['current_location_lng'] != null
              ? double.tryParse(json['current_location_lng'].toString())
              : null,
      lastLocationUpdate:
          json['last_location_update'] != null
              ? DateTime.parse(json['last_location_update'])
              : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'first_name': firstName,
      'last_name': lastName,
      'email': email,
      'date_joined': dateJoined.toIso8601String(),
      'role': role.name,
      'office': office?.toJson(),
      'phone': phone,
      'commission_rate': commissionRate,
      'current_location_lat': currentLocationLat,
      'current_location_lng': currentLocationLng,
      'last_location_update': lastLocationUpdate?.toIso8601String(),
    };
  }

  UserModel copyWith({
    int? id,
    String? username,
    String? firstName,
    String? lastName,
    String? email,
    DateTime? dateJoined,
    UserRole? role,
    OfficeModel? office,
    String? phone,
    double? commissionRate,
    double? currentLocationLat,
    double? currentLocationLng,
    DateTime? lastLocationUpdate,
  }) {
    return UserModel(
      id: id ?? this.id,
      username: username ?? this.username,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      email: email ?? this.email,
      dateJoined: dateJoined ?? this.dateJoined,
      role: role ?? this.role,
      office: office ?? this.office,
      phone: phone ?? this.phone,
      commissionRate: commissionRate ?? this.commissionRate,
      currentLocationLat: currentLocationLat ?? this.currentLocationLat,
      currentLocationLng: currentLocationLng ?? this.currentLocationLng,
      lastLocationUpdate: lastLocationUpdate ?? this.lastLocationUpdate,
    );
  }

  // Helper methods
  bool get isEmployee => role == UserRole.employee;
  bool get isManager => role == UserRole.manager;
  bool get isMaster => role == UserRole.master;

  bool canAccess(UserRole requiredRole) {
    return role.hasPermission(requiredRole);
  }

  String get displayRole => role.displayName;

  // Computed properties
  String get fullName {
    final parts = <String>[];
    if (firstName != null && firstName!.isNotEmpty) parts.add(firstName!);
    if (lastName != null && lastName!.isNotEmpty) parts.add(lastName!);
    return parts.isNotEmpty ? parts.join(' ') : username;
  }

  String get initials {
    if (firstName != null && lastName != null) {
      return '${firstName![0]}${lastName![0]}'.toUpperCase();
    } else if (firstName != null && firstName!.isNotEmpty) {
      return firstName![0].toUpperCase();
    } else if (username.isNotEmpty) {
      return username[0].toUpperCase();
    }
    return 'U';
  }

  // Convenience getters for backward compatibility
  String get name => fullName;
  String? get officeId => office?.id?.toString();
  String? get officeName => office?.name;

  @override
  String toString() {
    return 'UserModel(id: $id, username: $username, email: $email, role: ${role.name})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
