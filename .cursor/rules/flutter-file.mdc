---
description: 
globs: 
alwaysApply: false
---
# Flutter Development Rules

## Page Structure Organization

### Folder Structure
Create organized page folders within the `pages` directory using descriptive hierarchical naming:

```
pages/
├── auth/
│   ├── signup/
│   ├── signin/
│   └── profile/
├── home/
├── settings/
└── [feature_name]/
```

### Required Files Per Page
Each page folder must contain exactly two files:

1. **`[page_name]_page.dart`** - UI Implementation
2. **`[page_name]_controller.dart`** - Business Logic

**Example for signup page:**
```
auth/signup/
├── components/ // for page components
├── signup_page.dart
└── signup_controller.dart
```

### File Implementation Standards

#### Controller File (`[page_name]_controller.dart`)
- Use GetX controller pattern
- Extend `GetxController`
- Handle all business logic, state management, and API calls
- Name: `[PageName]Controller`

```dart
class SignupController extends GetxController {
  // All business logic here
}
```

#### Page File (`[page_name]_page.dart`)
- Use StatelessWidget only
- Import and bind the corresponding controller
- Focus on UI structure and layout
- Name: `[PageName]Page`

```dart
class SignupPage extends StatelessWidget {
  const SignupPage({Key? key}) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    // UI implementation with GetX controller binding
  }
}
```

### Component Guidelines
- **NO functional components** - Always use StatelessWidget classes
- Keep UI components as separate StatelessWidget classes when reusable
- Maintain clear separation between UI (page) and logic (controller)

### Naming Conventions
- Folders: lowercase with underscores (`signup`, `user_profile`)
- Files: lowercase with underscores (`signup_page.dart`)
- Classes: PascalCase (`SignupPage`, `SignupController`)

## Import Organization
Always organize imports in this exact order:
```dart
// 1. Dart core libraries
import 'dart:async';

// 2. Flutter framework
import 'package:flutter/material.dart';

// 3. Third-party packages (alphabetical)
import 'package:get/get.dart';

// 4. Internal app imports (alphabetical)
import '../../../core/constants/app_colors.dart';
import '../widgets/custom_button.dart';
```

## GetX Implementation Standards

### Controller Management
Use Get.put() to register controllers and Get.find() to access them:

```dart
class SignupPage extends StatelessWidget {
  const SignupPage({Key? key}) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    // Register controller
    Get.put(SignupController());
    
    return Scaffold(
      body: Column(
        children: [
          // Use Obx for reactive UI updates
          Obx(() => Get.find<SignupController>().isLoading.value
            ? CircularProgressIndicator()
            : LoginForm(),
          ),
        ],
      ),
    );
  }
}
```

### State Management
- Use `Rx` variables for all reactive state: `RxBool isLoading = false.obs;`
- Use `Get.put()` to register controllers
- Use `Get.find<ControllerType>()` to access controllers
- Use `Obx()` widget for all reactive UI updates

## Code Structure Standards

### Widget Organization
Structure widgets in this order within build method:
1. Controller initialization
2. Scaffold/main container
3. AppBar (if applicable)
4. Body content
5. Bottom navigation/floating action button

### Method Organization in Controllers
```dart
class SignupController extends GetxController {
  // 1. Properties/Variables
  final TextEditingController emailController = TextEditingController();
  RxBool isLoading = false.obs;
  
  // 2. Lifecycle methods
  @override
  void onInit() {
    super.onInit();
  }
  
  // 3. Public methods (called from UI)
  void signup() async {
    // Implementation
  }
  
  // 4. Private methods
  void _validateInput() {
    // Implementation
  }
  
  // 5. Dispose
  @override
  void onClose() {
    emailController.dispose();
    super.onClose();
  }
}
```

## Error Handling & Loading States

### Standard Loading Pattern
```dart
// In Controller
Future<void> performAction() async {
  try {
    isLoading.value = true;
    // API call or operation
    // Handle success
  } catch (e) {
    _handleError(e);
  } finally {
    isLoading.value = false;
  }
}

void _handleError(dynamic error) {
  Get.snackbar('Error', error.toString());
}
```

### UI Loading States
Always show loading indicators for async operations using Get.find():
```dart
Obx(() => Get.find<YourController>().isLoading.value 
  ? CircularProgressIndicator()
  : YourContentWidget()
)
```

## Routing & Navigation

### Navigation Standards
Use direct page navigation with GetX:

```dart
// Navigate to new page
Get.to(() => SignupPage());

// Navigate and remove current page from stack
Get.off(() => HomePage());

// Navigate and clear entire navigation stack
Get.offAll(() => HomePage());

// Navigate back
Get.back();

// Navigate back with result
Get.back(result: {'success': true});
```

## Routing & Navigation

### Navigation Standards
Use direct page navigation with typed arguments:

```dart
// Navigate to new page with typed arguments
Get.to(() => ProfilePage(
  userId: 123,
  userName: 'John Doe',
));

// Navigate and remove current page from stack
Get.off(() => HomePage());

// Navigate and clear entire navigation stack
Get.offAll(() => HomePage());

// Navigate back
Get.back();

// Navigate back with result
Get.back(result: {'success': true});
```

### Page Implementation with Arguments
Always define arguments as constructor parameters and register controller with Get.put():

```dart
// profile_page.dart
class ProfilePage extends StatelessWidget {
  final int userId;
  final String userName;
  
  const ProfilePage({
    Key? key,
    required this.userId,
    required this.userName,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    // Register controller with arguments
    Get.put(ProfileController(
      userId: userId,
      userName: userName,
    ));
    
    return Scaffold(
      body: Obx(() => Get.find<ProfileController>().isLoading.value
        ? CircularProgressIndicator()
        : ProfileContent(),
      ),
    );
  }
}
```

### Controller Implementation with Arguments
Controllers receive arguments through constructor:

```dart
// profile_controller.dart
class ProfileController extends GetxController {
  final int userId;
  final String userName;
  
  // Constructor receives typed arguments
  ProfileController({
    required this.userId,
    required this.userName,
  });
  
  // Other properties
  RxString displayName = ''.obs;
  RxBool isLoading = false.obs;
  
  @override
  void onInit() {
    super.onInit();
    loadUserProfile();
  }
  
  void loadUserProfile() async {
    // Use userId and userName here
    displayName.value = userName;
  }
}
```

### Optional Arguments Pattern
For optional arguments, use nullable types and Get.put():

```dart
// Page with optional arguments
class SettingsPage extends StatelessWidget {
  final String? initialTab;
  final bool? showAdvanced;
  
  const SettingsPage({
    Key? key,
    this.initialTab,
    this.showAdvanced,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    // Register controller with optional arguments
    Get.put(SettingsController(
      initialTab: initialTab,
      showAdvanced: showAdvanced ?? false,
    ));
    
    return Scaffold(
      body: Obx(() => Get.find<SettingsController>().currentTab.value == 'privacy'
        ? PrivacySettings()
        : GeneralSettings(),
      ),
    );
  }
}

// Navigation with optional arguments
Get.to(() => SettingsPage(
  initialTab: 'privacy',
  showAdvanced: true,
));

// Navigation without optional arguments
Get.to(() => SettingsPage());
```

This structure ensures maintainable, scalable Flutter applications with clear separation of concerns and consistent code patterns.